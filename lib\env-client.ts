"use client"

// Проверяем и экспортируем переменные окружения для клиентской части
export const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ""
export const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ""

// Проверяем наличие переменных окружения
export const checkEnvVariables = () => {
  const missingVars = []

  if (!supabaseUrl) missingVars.push("NEXT_PUBLIC_SUPABASE_URL")
  if (!supabaseAnonKey) missingVars.push("NEXT_PUBLIC_SUPABASE_ANON_KEY")

  return {
    isValid: missingVars.length === 0,
    missingVars,
  }
}
