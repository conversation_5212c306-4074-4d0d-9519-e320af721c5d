import { createServerClient } from "@/lib/supabase/server"
import { ActionForm } from "../action-form"

export default async function NewActionPage() {
  const supabase = await createServerClient()

  // Fetch task groups for selection
  const { data: groups } = await supabase.from("task_groups").select("*").order("name", { ascending: true })

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Добавить действие</h1>
      <ActionForm groups={groups} />
    </div>
  )
}
