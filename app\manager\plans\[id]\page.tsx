import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { createServerClient } from "@/lib/supabase/server"
import Link from "next/link"
import { notFound } from "next/navigation"
import { format } from "date-fns"
import { ru } from "date-fns/locale"
import { Pencil, Calendar } from "lucide-react"
import { Badge } from "@/components/ui/badge"

export default async function PlanDetailPage({ params }: { params: { id: string } }) {
  const supabase = await createServerClient()

  // Fetch plan data with client information
  const { data: plan, error } = await supabase
    .from("plans")
    .select(
      `
      *,
      client:client_id(id, full_name)
    `,
    )
    .eq("id", params.id)
    .single()

  if (error || !plan) {
    notFound()
  }

  // Fetch plan items
  const { data: planItems } = await supabase
    .from("plan_items")
    .select(
      `
      *,
      action:action_id(id, text, group_id),
      interval:interval_id(id, name)
    `,
    )
    .eq("plan_id", params.id)
    .order("position", { ascending: true })

  // Format dates safely
  const formatDateSafely = (dateString: string | null) => {
    if (!dateString) return "Не указана"
    try {
      return format(new Date(dateString), "dd MMMM yyyy", { locale: ru })
    } catch (e) {
      return "Некорректная дата"
    }
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">{plan.name}</h1>
        <div className="flex space-x-2">
          <Link href={`/manager/plans/${params.id}/edit`}>
            <Button variant="outline">
              <Pencil className="mr-2 h-4 w-4" /> Редактировать
            </Button>
          </Link>
          <Link href={`/manager/plans/${params.id}/generate`}>
            <Button>
              <Calendar className="mr-2 h-4 w-4" /> Генерировать задачи
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Информация о плане</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-2">
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Название:</dt>
                <dd className="col-span-2">{plan.name}</dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Клиент:</dt>
                <dd className="col-span-2">
                  {plan.client?.id ? (
                    <Link href={`/manager/clients/${plan.client?.id}`} className="text-blue-600 hover:underline">
                      {plan.client?.full_name}
                    </Link>
                  ) : (
                    "Без клиента"
                  )}
                </dd>
              </div>
              {plan.description && (
                <div className="grid grid-cols-3 gap-1">
                  <dt className="font-medium text-gray-500">Описание:</dt>
                  <dd className="col-span-2">{plan.description}</dd>
                </div>
              )}
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Статус:</dt>
                <dd className="col-span-2">
                  {plan.is_active ? (
                    <Badge className="bg-green-100 text-green-800">Активный</Badge>
                  ) : (
                    <Badge variant="outline">Неактивный</Badge>
                  )}
                </dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Создан:</dt>
                <dd className="col-span-2">{formatDateSafely(plan.created_at)}</dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Дата начала:</dt>
                <dd className="col-span-2">{plan.start_date ? formatDateSafely(plan.start_date) : "Не указана"}</dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Дата окончания:</dt>
                <dd className="col-span-2">{plan.end_date ? formatDateSafely(plan.end_date) : "Не указана"}</dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Количество действий:</dt>
                <dd className="col-span-2">{planItems?.length || 0}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Элементы плана</CardTitle>
          </CardHeader>
          <CardContent>
            {planItems && planItems.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">№</th>
                      <th className="text-left py-2">Действие</th>
                      <th className="text-left py-2">Интервал</th>
                      <th className="text-left py-2">Время</th>
                      <th className="text-left py-2">Повторение</th>
                      <th className="text-left py-2">Дата выполнения</th>
                    </tr>
                  </thead>
                  <tbody>
                    {planItems.map((item, index) => (
                      <tr key={item.id} className="border-b">
                        <td className="py-2">{index + 1}</td>
                        <td className="py-2">{item.action?.text || "Неизвестное действие"}</td>
                        <td className="py-2">{item.interval?.name || "Не указан"}</td>
                        <td className="py-2">{item.time || "Не указано"}</td>
                        <td className="py-2">
                          {item.recurrence_type === "once"
                            ? "Однократно"
                            : item.recurrence_type === "daily"
                              ? "Ежедневно"
                              : item.recurrence_type === "weekly"
                                ? "Еженедельно"
                                : item.recurrence_type === "monthly"
                                  ? "Ежемесячно"
                                  : item.recurrence_type === "yearly"
                                    ? "Ежегодно"
                                    : "Не указано"}
                        </td>
                        <td className="py-2">
                          {item.recurrence_type === "once" && item.execution_date
                            ? (() => {
                              try {
                                return format(new Date(item.execution_date), "dd.MM.yyyy")
                              } catch (e) {
                                return "Некорректная дата"
                              }
                            })()
                            : "-"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-center text-muted-foreground">Нет элементов в плане</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
