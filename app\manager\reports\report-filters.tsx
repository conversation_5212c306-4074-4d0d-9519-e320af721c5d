"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { ru } from "date-fns/locale"
import { CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"

interface ReportFiltersProps {
  clients: { id: string; full_name: string }[]
  intervals: { id: string; name: string }[]
  groups: { id: string; name: string }[]
  onApplyFilters: (filters: any) => void
  showClientFilter?: boolean
  showCommentFilter?: boolean
}

export function ReportFilters({
  clients,
  intervals,
  groups,
  onApplyFilters,
  showClientFilter = true,
  showCommentFilter = false,
}: ReportFiltersProps) {
  const [clientId, setClientId] = useState<string>("all")
  const [intervalId, setIntervalId] = useState<string>("all")
  const [groupId, setGroupId] = useState<string>("all")
  const [status, setStatus] = useState<string>("all")
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)
  const [hasComment, setHasComment] = useState<string>("any")

  const handleApplyFilters = () => {
    const filters: any = {}

    if (clientId !== "all") filters.client_id = clientId
    if (intervalId !== "all") filters.interval_id = intervalId
    if (groupId !== "all") filters.group_id = groupId
    if (status !== "all") filters.status = status
    if (startDate) filters.start_date = startDate
    if (endDate) filters.end_date = endDate
    if (hasComment === "yes") filters.has_comment = true
    if (hasComment === "no") filters.has_comment = false

    onApplyFilters(filters)
  }

  const handleResetFilters = () => {
    setClientId("all")
    setIntervalId("all")
    setGroupId("all")
    setStatus("all")
    setStartDate(undefined)
    setEndDate(undefined)
    setHasComment("any")
    onApplyFilters({})
  }

  return (
    <Card className="mb-6">
      <CardContent className="pt-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {showClientFilter && (
            <div className="space-y-2">
              <Label htmlFor="client">Клиент</Label>
              <Select value={clientId} onValueChange={setClientId}>
                <SelectTrigger id="client">
                  <SelectValue placeholder="Все клиенты" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Все клиенты</SelectItem>
                  {clients.map((client) => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.full_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="interval">Интервал</Label>
            <Select value={intervalId} onValueChange={setIntervalId}>
              <SelectTrigger id="interval">
                <SelectValue placeholder="Все интервалы" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Все интервалы</SelectItem>
                {intervals.map((interval) => (
                  <SelectItem key={interval.id} value={interval.id}>
                    {interval.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="group">Группа</Label>
            <Select value={groupId} onValueChange={setGroupId}>
              <SelectTrigger id="group">
                <SelectValue placeholder="Все группы" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Все группы</SelectItem>
                {groups.map((group) => (
                  <SelectItem key={group.id} value={group.id}>
                    {group.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Статус</Label>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger id="status">
                <SelectValue placeholder="Все статусы" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Все статусы</SelectItem>
                <SelectItem value="pending">Ожидает</SelectItem>
                <SelectItem value="in_progress">В процессе</SelectItem>
                <SelectItem value="completed">Выполнено</SelectItem>
                <SelectItem value="cancelled">Отменено</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Дата начала</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn("w-full justify-start text-left font-normal", !startDate && "text-muted-foreground")}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {startDate ? format(startDate, "dd.MM.yyyy") : "Выберите дату"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar mode="single" selected={startDate} onSelect={setStartDate} initialFocus locale={ru} />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <Label>Дата окончания</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn("w-full justify-start text-left font-normal", !endDate && "text-muted-foreground")}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {endDate ? format(endDate, "dd.MM.yyyy") : "Выберите дату"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar mode="single" selected={endDate} onSelect={setEndDate} initialFocus locale={ru} />
              </PopoverContent>
            </Popover>
          </div>

          {showCommentFilter && (
            <div className="space-y-2">
              <Label htmlFor="hasComment">Комментарий</Label>
              <Select value={hasComment} onValueChange={setHasComment}>
                <SelectTrigger id="hasComment">
                  <SelectValue placeholder="Любой" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="any">Любой</SelectItem>
                  <SelectItem value="yes">Есть</SelectItem>
                  <SelectItem value="no">Нет</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-2 mt-6">
          <Button variant="outline" onClick={handleResetFilters}>
            Сбросить
          </Button>
          <Button onClick={handleApplyFilters}>Применить</Button>
        </div>
      </CardContent>
    </Card>
  )
}
