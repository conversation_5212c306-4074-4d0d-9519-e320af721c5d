"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { createClient } from "@/lib/supabase/client"
import { toast } from "@/hooks/use-toast"
import { Plus, Trash, Edit, ArrowUp, ArrowDown, Clock, CalendarIcon } from "lucide-react"
// Добавим импорт для DatePicker
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { ru } from "date-fns/locale"

// Обновим интерфейс PlanItemsEditorProps, добавив даты плана
interface PlanItemsEditorProps {
  planId: string
  planItems: any[]
  setPlanItems: (items: any[]) => void
  actions: any[]
  intervals: any[]
  isDefaultPlan?: boolean
  planStartDate?: Date
  planEndDate?: Date
}

// Обновим функцию PlanItemsEditor, добавив параметры дат плана
export function PlanItemsEditor({
  planId,
  planItems,
  setPlanItems,
  actions,
  intervals,
  isDefaultPlan = false,
  planStartDate,
  planEndDate,
}: PlanItemsEditorProps) {
  // Добавим состояние для даты выполнения
  const [executionDate, setExecutionDate] = useState<Date | undefined>(
    planStartDate ? new Date(planStartDate) : new Date(),
  )

  // Обновим состояние currentItem, добавив поле execution_date
  const [currentItem, setCurrentItem] = useState<any>({
    action_id: "",
    interval_id: "",
    time: "",
    recurrence_type: "once",
    recurrence_value: {},
    execution_date: null,
  })
  const [editIndex, setEditIndex] = useState<number | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false)
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
  const supabase = createClient()

  // Prepare time options
  const timeOptions = []
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const formattedHour = hour.toString().padStart(2, "0")
      const formattedMinute = minute.toString().padStart(2, "0")
      timeOptions.push(`${formattedHour}:${formattedMinute}`)
    }
  }

  // Recurrence options
  const recurrenceOptions = [
    { value: "once", label: "Однократно" },
    { value: "daily", label: "Ежедневно" },
    { value: "weekly", label: "Еженедельно" },
    { value: "monthly", label: "Ежемесячно" },
    { value: "yearly", label: "Ежегодно" },
  ]

  // Обновим функцию handleAddItem
  const handleAddItem = (e: React.MouseEvent) => {
    e.preventDefault() // Предотвращаем отправку формы
    setCurrentItem({
      action_id: "",
      interval_id: "",
      time: "",
      recurrence_type: "once",
      recurrence_value: {},
      execution_date: planStartDate ? format(new Date(planStartDate), "yyyy-MM-dd") : format(new Date(), "yyyy-MM-dd"),
    })
    setExecutionDate(planStartDate ? new Date(planStartDate) : new Date())
    setEditIndex(null)
    setIsDialogOpen(true)
  }

  // Обновим функцию handleEditItem
  const handleEditItem = (item: any, index: number) => {
    setCurrentItem({
      ...item,
      action_id: item.action?.id || item.action_id,
      interval_id: item.interval?.id || item.interval_id,
      execution_date: item.execution_date || null,
    })
    setExecutionDate(item.execution_date ? new Date(item.execution_date) : undefined)
    setEditIndex(index)
    setIsDialogOpen(true)
  }

  // Save item (add or update)
  const handleSaveItem = async () => {
    if (!currentItem.action_id) {
      toast({
        title: "Ошибка",
        description: "Выберите действие",
        variant: "destructive",
      })
      return
    }

    // Проверим, что для однократного выполнения указана дата
    if (currentItem.recurrence_type === "once" && !currentItem.execution_date) {
      toast({
        title: "Ошибка",
        description: "Для однократного выполнения необходимо указать дату",
        variant: "destructive",
      })
      return
    }

    // Проверим, что дата выполнения находится в рамках дат плана
    if (currentItem.execution_date && planStartDate && planEndDate) {
      const execDate = new Date(currentItem.execution_date)
      const startDate = new Date(planStartDate)
      const endDate = new Date(planEndDate)

      if (execDate < startDate || execDate > endDate) {
        toast({
          title: "Ошибка",
          description: "Дата выполнения должна быть в рамках дат начала и окончания плана",
          variant: "destructive",
        })
        return
      }
    }

    setIsSubmitting(true)

    try {
      const itemData = {
        ...(editIndex !== null ? { id: planItems[editIndex].id } : {}),
        [isDefaultPlan ? "default_plan_id" : "plan_id"]: planId,
        action_id: currentItem.action_id,
        interval_id: currentItem.interval_id || null,
        time: currentItem.time || null,
        position: editIndex !== null ? planItems[editIndex].position : planItems.length,
        recurrence_type: currentItem.recurrence_type || "once",
        recurrence_value: currentItem.recurrence_value || {},
        execution_date: currentItem.recurrence_type === "once" ? currentItem.execution_date : null,
      }

      if (editIndex !== null) {
        // Update existing item
        const { error } = await supabase
          .from(isDefaultPlan ? "default_plan_items" : "plan_items")
          .update(itemData)
          .eq("id", planItems[editIndex].id)

        if (error) throw error

        // Update local state
        const updatedItems = [...planItems]
        updatedItems[editIndex] = {
          ...planItems[editIndex],
          ...itemData,
          action: actions.find((a) => a.id === currentItem.action_id),
          interval: intervals.find((i) => i.id === currentItem.interval_id),
        }
        setPlanItems(updatedItems)

        toast({
          title: "Элемент обновлен",
          description: "Элемент плана успешно обновлен",
        })
      } else {
        // Add new item
        const { data, error } = await supabase
          .from(isDefaultPlan ? "default_plan_items" : "plan_items")
          .insert(itemData)
          .select()

        if (error) throw error

        if (data && data.length > 0) {
          // Add to local state with related data
          const newItem = {
            ...data[0],
            action: actions.find((a) => a.id === currentItem.action_id),
            interval: intervals.find((i) => i.id === currentItem.interval_id),
          }
          setPlanItems([...planItems, newItem])

          toast({
            title: "Элемент добавлен",
            description: "Новый элемент плана успешно добавлен",
          })
        }
      }

      setIsDialogOpen(false)
    } catch (error: any) {
      toast({
        title: "Ошибка",
        description: error.message || "Произошла ошибка при сохранении элемента",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Delete item
  const handleDeleteItem = async (id: string, index: number) => {
    if (!confirm("Вы уверены, что хотите удалить этот элемент плана?")) {
      return
    }

    try {
      const { error } = await supabase
        .from(isDefaultPlan ? "default_plan_items" : "plan_items")
        .delete()
        .eq("id", id)

      if (error) throw error

      // Update local state
      const updatedItems = [...planItems]
      updatedItems.splice(index, 1)
      setPlanItems(updatedItems)

      toast({
        title: "Элемент удален",
        description: "Элемент плана успешно удален",
      })
    } catch (error: any) {
      toast({
        title: "Ошибка",
        description: error.message || "Произошла ошибка при удалении элемента",
        variant: "destructive",
      })
    }
  }

  // Move item up in the list
  const handleMoveUp = async (index: number) => {
    if (index === 0) return

    try {
      const updatedItems = [...planItems]
      const item = updatedItems[index]
      const prevItem = updatedItems[index - 1]

      // Swap positions
      const tempPosition = item.position
      item.position = prevItem.position
      prevItem.position = tempPosition

      // Update in database
      const updates = [
        supabase
          .from(isDefaultPlan ? "default_plan_items" : "plan_items")
          .update({ position: item.position })
          .eq("id", item.id),
        supabase
          .from(isDefaultPlan ? "default_plan_items" : "plan_items")
          .update({ position: prevItem.position })
          .eq("id", prevItem.id),
      ]

      const results = await Promise.all(updates)
      if (results.some((result) => result.error)) {
        throw new Error("Ошибка при обновлении позиций")
      }

      // Swap in array
      updatedItems[index] = prevItem
      updatedItems[index - 1] = item
      setPlanItems(updatedItems)
    } catch (error: any) {
      toast({
        title: "Ошибка",
        description: error.message || "Произошла ошибка при изменении порядка",
        variant: "destructive",
      })
    }
  }

  // Move item down in the list
  const handleMoveDown = async (index: number) => {
    if (index === planItems.length - 1) return

    try {
      const updatedItems = [...planItems]
      const item = updatedItems[index]
      const nextItem = updatedItems[index + 1]

      // Swap positions
      const tempPosition = item.position
      item.position = nextItem.position
      nextItem.position = tempPosition

      // Update in database
      const updates = [
        supabase
          .from(isDefaultPlan ? "default_plan_items" : "plan_items")
          .update({ position: item.position })
          .eq("id", item.id),
        supabase
          .from(isDefaultPlan ? "default_plan_items" : "plan_items")
          .update({ position: nextItem.position })
          .eq("id", nextItem.id),
      ]

      const results = await Promise.all(updates)
      if (results.some((result) => result.error)) {
        throw new Error("Ошибка при обновлении позиций")
      }

      // Swap in array
      updatedItems[index] = nextItem
      updatedItems[index + 1] = item
      setPlanItems(updatedItems)
    } catch (error: any) {
      toast({
        title: "Ошибка",
        description: error.message || "Произошла ошибка при изменении порядка",
        variant: "destructive",
      })
    }
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Элементы плана</CardTitle>
          <Button onClick={handleAddItem} type="button">
            <Plus className="mr-2 h-4 w-4" /> Добавить элемент
          </Button>
        </CardHeader>
        <CardContent>
          {planItems.length === 0 ? (
            <p className="text-center text-muted-foreground py-4">Нет элементов в плане</p>
          ) : (
            <Table>
              <TableHeader>
                {/* Обновим отображение элементов в таблице, добавив колонку для даты выполнения */}
                <TableRow>
                  <TableHead className="w-[50px]">№</TableHead>
                  <TableHead>Действие</TableHead>
                  <TableHead>Интервал</TableHead>
                  <TableHead>Время</TableHead>
                  <TableHead>Повторение</TableHead>
                  <TableHead>Дата выполнения</TableHead>
                  <TableHead className="text-right">Действия</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {planItems.map((item, index) => (
                  <TableRow key={item.id}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{item.action?.text || "Неизвестное действие"}</TableCell>
                    <TableCell>{item.interval?.name || "Не указан"}</TableCell>
                    <TableCell>{item.time || "Не указано"}</TableCell>
                    <TableCell>
                      {item.recurrence_type === "once"
                        ? "Однократно"
                        : item.recurrence_type === "daily"
                          ? "Ежедневно"
                          : item.recurrence_type === "weekly"
                            ? "Еженедельно"
                            : item.recurrence_type === "monthly"
                              ? "Ежемесячно"
                              : item.recurrence_type === "yearly"
                                ? "Ежегодно"
                                : "Не указано"}
                    </TableCell>
                    {/* И в теле таблицы добавим ячейку для даты выполнения */}
                    <TableCell>
                      {item.recurrence_type === "once" && item.execution_date
                        ? format(new Date(item.execution_date), "dd.MM.yyyy")
                        : "-"}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleMoveUp(index)}
                          disabled={index === 0}
                        >
                          <ArrowUp className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleMoveDown(index)}
                          disabled={index === planItems.length - 1}
                        >
                          <ArrowDown className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="icon" onClick={() => handleEditItem(item, index)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleDeleteItem(item.id, index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Dialog for adding/editing items */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{editIndex !== null ? "Редактировать элемент" : "Добавить элемент"}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="action" className="text-sm font-medium">
                Действие
              </label>
              <Select
                value={currentItem?.action_id || "none"}
                onValueChange={(value) => setCurrentItem({ ...currentItem, action_id: value })}
              >
                <SelectTrigger id="action">
                  <SelectValue placeholder="Выберите действие" />
                </SelectTrigger>
                <SelectContent>
                  {actions.map((action) => (
                    <SelectItem key={action.id} value={action.id}>
                      {action.text}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <label htmlFor="interval" className="text-sm font-medium">
                Интервал
              </label>
              <Select
                value={currentItem?.interval_id || "none"}
                onValueChange={(value) => setCurrentItem({ ...currentItem, interval_id: value })}
              >
                <SelectTrigger id="interval">
                  <SelectValue placeholder="Выберите интервал" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Не указан</SelectItem>
                  {intervals.map((interval) => (
                    <SelectItem key={interval.id} value={interval.id}>
                      {interval.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <label htmlFor="time" className="text-sm font-medium">
                Время
              </label>
              <Select
                value={currentItem?.time || "none"}
                onValueChange={(value) => setCurrentItem({ ...currentItem, time: value })}
              >
                <SelectTrigger id="time">
                  <SelectValue placeholder="Выберите время">
                    {currentItem?.time ? (
                      <div className="flex items-center">
                        <Clock className="mr-2 h-4 w-4" />
                        {currentItem.time}
                      </div>
                    ) : (
                      "Выберите время"
                    )}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Не указано</SelectItem>
                  {timeOptions.map((time) => (
                    <SelectItem key={time} value={time}>
                      {time}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <label htmlFor="recurrence" className="text-sm font-medium">
                Повторение
              </label>
              <Select
                value={currentItem?.recurrence_type || "once"}
                onValueChange={(value) => setCurrentItem({ ...currentItem, recurrence_type: value })}
              >
                <SelectTrigger id="recurrence">
                  <SelectValue placeholder="Выберите тип повторения" />
                </SelectTrigger>
                <SelectContent>
                  {recurrenceOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Additional fields for recurrence settings based on type */}
            {currentItem?.recurrence_type === "weekly" && (
              <div className="grid gap-2">
                <label className="text-sm font-medium">Дни недели</label>
                <div className="flex flex-wrap gap-2">
                  {["Пн", "Вт", "Ср", "Чт", "Пт", "Сб", "Вс"].map((day, index) => (
                    <Button
                      key={index}
                      type="button"
                      variant={currentItem?.recurrence_value?.days?.includes(index + 1) ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        const days = currentItem?.recurrence_value?.days || []
                        const dayValue = index + 1
                        const newDays = days.includes(dayValue)
                          ? days.filter((d: number) => d !== dayValue)
                          : [...days, dayValue]
                        setCurrentItem({
                          ...currentItem,
                          recurrence_value: { ...currentItem.recurrence_value, days: newDays },
                        })
                      }}
                    >
                      {day}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {currentItem?.recurrence_type === "monthly" && (
              <div className="grid gap-2">
                <label className="text-sm font-medium">Дни месяца</label>
                <Input
                  type="text"
                  placeholder="Например: 1, 15, 30"
                  value={currentItem?.recurrence_value?.days?.join(", ") || ""}
                  onChange={(e) => {
                    const daysStr = e.target.value
                    const days = daysStr
                      .split(",")
                      .map((d) => Number.parseInt(d.trim()))
                      .filter((d) => !isNaN(d) && d > 0 && d <= 31)
                    setCurrentItem({
                      ...currentItem,
                      recurrence_value: { ...currentItem.recurrence_value, days },
                    })
                  }}
                />
              </div>
            )}

            {currentItem?.recurrence_type === "yearly" && (
              <div className="grid gap-2">
                <label className="text-sm font-medium">Даты (месяц-день)</label>
                <Input
                  type="text"
                  placeholder="Например: 1-1, 12-31"
                  value={currentItem?.recurrence_value?.dates?.join(", ") || ""}
                  onChange={(e) => {
                    const datesStr = e.target.value
                    const dates = datesStr
                      .split(",")
                      .map((d) => d.trim())
                      .filter((d) => /^\d{1,2}-\d{1,2}$/.test(d))
                    setCurrentItem({
                      ...currentItem,
                      recurrence_value: { ...currentItem.recurrence_value, dates },
                    })
                  }}
                />
              </div>
            )}
            {/* Добавим поле для выбора даты выполнения в диалоге добавления/редактирования элемента */}
            {/* Добавим это после поля выбора типа повторения */}
            {currentItem?.recurrence_type === "once" && (
              <div className="grid gap-2">
                <label className="text-sm font-medium">Дата выполнения</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {executionDate ? format(executionDate, "PPP", { locale: ru }) : <span>Выберите дату</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={executionDate}
                      onSelect={(date) => {
                        setExecutionDate(date)
                        if (date) {
                          setCurrentItem({
                            ...currentItem,
                            execution_date: format(date, "yyyy-MM-dd"),
                          })
                        }
                      }}
                      disabled={(date) => {
                        if (planStartDate && planEndDate) {
                          const start = new Date(planStartDate)
                          const end = new Date(planEndDate)
                          return date < start || date > end
                        }
                        return false
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Отмена
            </Button>
            <Button onClick={handleSaveItem} disabled={isSubmitting}>
              {isSubmitting ? "Сохранение..." : "Сохранить"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
