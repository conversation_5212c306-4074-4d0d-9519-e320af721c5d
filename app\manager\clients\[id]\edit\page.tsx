import { createServerClient } from "@/lib/supabase/server"
import { ClientForm } from "../../client-form"
import { notFound } from "next/navigation"

export default async function EditClientPage({ params }: { params: { id: string } }) {
  const supabase = await createServerClient()

  // Fetch client data
  const { data: client, error } = await supabase.from("clients").select("*").eq("id", params.id).single()

  if (error || !client) {
    notFound()
  }

  // Fetch client relatives
  const { data: relatives } = await supabase
    .from("client_relatives")
    .select("*")
    .eq("client_id", params.id)
    .order("created_at", { ascending: true })

  // Add relatives to client object
  const clientWithRelatives = {
    ...client,
    relatives: relatives || [],
  }

  // Fetch users for selection
  const { data: users } = await supabase.auth.admin.listUsers()

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Редактировать клиента</h1>
      <ClientForm client={clientWithRelatives} users={users?.users} />
    </div>
  )
}
