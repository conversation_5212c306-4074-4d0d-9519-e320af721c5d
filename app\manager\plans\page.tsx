import { Button } from "@/components/ui/button"
import { createServerClient } from "@/lib/supabase/server"
import Link from "next/link"
import { Plus, Calendar } from "lucide-react"
import { format } from "date-fns"

export default async function PlansPage() {
  const supabase = await createServerClient()

  // Fetch plans with client information
  const { data: plans, error } = await supabase
    .from("plans")
    .select(`
    *,
    client:client_id (
      id,
      full_name
    )
  `)
    .order("created_at", { ascending: false })

  if (error) {
    console.error("Error fetching plans:", error)
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Планы</h1>
        <Link href="/manager/plans/new">
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Создать план
          </Button>
        </Link>
      </div>

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Название
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Клиент
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Статус
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Создан
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Действия
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {plans && plans.length > 0 ? (
                plans.map((plan) => (
                  <tr key={plan.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{plan.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {plan.client ? (
                        <Link href={`/manager/clients/${plan.client?.id}`} className="hover:underline text-blue-600">
                          {plan.client?.full_name}
                        </Link>
                      ) : (
                        "Без клиента"
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${plan.is_active ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                          }`}
                      >
                        {plan.is_active ? "Активный" : "Неактивный"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {format(new Date(plan.created_at), "dd.MM.yyyy")}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Link href={`/manager/plans/${plan.id}`}>
                          <Button variant="outline" size="sm">
                            Просмотр
                          </Button>
                        </Link>
                        <Link href={`/manager/plans/${plan.id}/edit`}>
                          <Button variant="outline" size="sm">
                            Изменить
                          </Button>
                        </Link>
                        <Link href={`/manager/plans/${plan.id}/generate`}>
                          <Button variant="outline" size="sm">
                            <Calendar className="h-4 w-4 mr-1" />
                            Задачи
                          </Button>
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                    {error ? "Ошибка загрузки планов" : "Планы не найдены"}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
