"use client"

import type React from "react"

import { useState, useRef, useEffect, useCallback } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Trash, Tag, X, Plus, Menu, LogOut, RefreshCw, AlertCircle } from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle } from "@/components/ui/drawer"
import { useMediaQuery } from "@/hooks/use-media-query"
import { useAuth } from "@/components/auth-provider"
import type { Task } from "@/lib/actions"
import { createTask, updateTask, deleteTask, addTagToTask, removeTagFromTask, getTasks } from "@/lib/actions"
import { useToast } from "@/hooks/use-toast"
import { useRealtimeSubscription } from "@/hooks/use-realtime"
import { createClient } from "@/lib/supabase/client"

interface TodoListProps {
  initialTasks: Task[]
  initialTags: { id: string; name: string }[]
}

export default function TodoListRealtime({ initialTasks, initialTags }: TodoListProps) {
  const [tasks, setTasks] = useState<Task[]>(initialTasks)
  const [newTask, setNewTask] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [newTag, setNewTag] = useState("")
  const [editingTaskId, setEditingTaskId] = useState<string | null>(null)
  const [selectedTask, setSelectedTask] = useState<Task | null>(null)
  const [taskComment, setTaskComment] = useState("")
  const [isTaskDialogOpen, setIsTaskDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const isMobile = useMediaQuery("(max-width: 640px)")
  const touchStartX = useRef<number | null>(null)
  const touchStartY = useRef<number | null>(null)
  const currentTaskRef = useRef<string | null>(null)
  const { user, signOut, refreshSession } = useAuth()
  const { toast } = useToast()
  const supabase = createClient()

  // Подписываемся на изменения в реальном времени
  const { channel, error: realtimeError } = useRealtimeSubscription()

  // Функция для обновления задач
  const refreshTasks = useCallback(async () => {
    setIsRefreshing(true)
    try {
      const { tasks: updatedTasks, tags: updatedTags } = await getTasks()
      setTasks(updatedTasks)
      setError(null)
      toast({
        title: "Данные обновлены",
        description: "Список задач успешно обновлен",
      })
    } catch (err: any) {
      console.error("Ошибка обновления задач:", err)
      setError(err.message || "Не удалось обновить список задач")

      // Если ошибка связана с аутентификацией, пытаемся обновить сессию
      if (err.message?.includes("auth") || err.message?.includes("JWT")) {
        await refreshSession()
      }

      toast({
        title: "Ошибка обновления",
        description: err.message || "Не удалось обновить список задач",
        variant: "destructive",
      })
    } finally {
      setIsRefreshing(false)
    }
  }, [toast, refreshSession])

  // Обновляем задачи при изменениях в базе данных
  useEffect(() => {
    if (realtimeError) {
      console.error("Ошибка Realtime:", realtimeError)
      setError(`Ошибка подписки на обновления: ${realtimeError.message}`)
    }
  }, [realtimeError])

  // Обработчик для обновления данных при изменениях
  useEffect(() => {
    if (!channel) return

    const handleBroadcast = (payload: any) => {
      console.log("Получено broadcast сообщение:", payload)
      refreshTasks()
    }

    try {
      // Добавляем обработчик для канала
      channel.on("broadcast", { event: "tasks-update" }, handleBroadcast)
    } catch (err) {
      console.error("Ошибка при добавлении обработчика broadcast:", err)
    }

    return () => {
      try {
        // Проверяем, что канал существует и имеет метод off
        if (channel && typeof channel.off === "function") {
          channel.off("broadcast", { event: "tasks-update" })
        }
      } catch (err) {
        console.error("Ошибка при удалении обработчика broadcast:", err)
      }
    }
  }, [channel, refreshTasks])

  // Периодически обновляем данные для предотвращения устаревания
  useEffect(() => {
    const intervalId = setInterval(
      () => {
        if (user) {
          refreshTasks()
        }
      },
      5 * 60 * 1000,
    ) // Каждые 5 минут

    return () => clearInterval(intervalId)
  }, [user, refreshTasks])

  // Get all unique tags from tasks
  const allTags = Array.from(new Set(tasks.flatMap((task) => task.tags))).sort()

  // Filter tasks based on active tab
  const filteredTasks = tasks.filter((task) => {
    if (activeTab === "all") return true
    if (activeTab === "completed") return task.completed
    return task.tags.includes(activeTab)
  })

  // Add a new task
  const handleAddTask = async () => {
    if (newTask.trim() === "") return

    setIsLoading(true)
    try {
      const { task, error } = await createTask(newTask)
      if (error) {
        throw new Error(error)
      }

      if (task) {
        setTasks([{ ...task, tags: [] }, ...tasks])
        setNewTask("")
        toast({
          title: "Задача добавлена",
          description: "Новая задача успешно создана",
        })

        // Уведомляем другие вкладки об изменении
        try {
          if (channel && typeof channel.send === "function") {
            await channel.send({
              type: "broadcast",
              event: "tasks-update",
              payload: { type: "add" },
            })
          }
        } catch (err) {
          console.error("Ошибка при отправке broadcast:", err)
        }
      }
    } catch (err: any) {
      setError(err.message)
      toast({
        title: "Ошибка",
        description: err.message,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Toggle task completion
  const handleToggleTaskCompletion = async (id: string, completed: boolean) => {
    try {
      const status = completed ? "completed" : "pending"
      const { task, error } = await updateTask(id, { completed, status })

      if (error) {
        throw new Error(error)
      }

      setTasks(tasks.map((t) => (t.id === id ? { ...t, completed, status } : t)))

      // Уведомляем другие вкладки об изменении
      try {
        if (channel && typeof channel.send === "function") {
          await channel.send({
            type: "broadcast",
            event: "tasks-update",
            payload: { type: "update", id },
          })
        }
      } catch (err) {
        console.error("Ошибка при отправке broadcast:", err)
      }
    } catch (err: any) {
      toast({
        title: "Ошибка",
        description: err.message,
        variant: "destructive",
      })
    }
  }

  // Remove a task
  const handleRemoveTask = async (id: string) => {
    try {
      const { error } = await deleteTask(id)

      if (error) {
        throw new Error(error)
      }

      setTasks(tasks.filter((task) => task.id !== id))
      toast({
        title: "Задача удалена",
        description: "Задача успешно удалена",
      })

      // Уведомляем другие вкладки об изменении
      try {
        if (channel && typeof channel.send === "function") {
          await channel.send({
            type: "broadcast",
            event: "tasks-update",
            payload: { type: "delete", id },
          })
        }
      } catch (err) {
        console.error("Ошибка при отправке broadcast:", err)
      }
    } catch (err: any) {
      toast({
        title: "Ошибка",
        description: err.message,
        variant: "destructive",
      })
    }
  }

  // Add a tag to a task
  const handleAddTagToTask = async (taskId: string) => {
    if (newTag.trim() === "") return

    try {
      const { error } = await addTagToTask(taskId, newTag)

      if (error) {
        throw new Error(error)
      }

      setTasks(
        tasks.map((task) => {
          if (task.id === taskId && !task.tags.includes(newTag)) {
            return { ...task, tags: [...task.tags, newTag] }
          }
          return task
        }),
      )

      setNewTag("")
      setEditingTaskId(null)
      toast({
        title: "Тег добавлен",
        description: `Тег "${newTag}" добавлен к задаче`,
      })

      // Уведомляем другие вкладки об изменении
      try {
        if (channel && typeof channel.send === "function") {
          await channel.send({
            type: "broadcast",
            event: "tasks-update",
            payload: { type: "tag", id: taskId },
          })
        }
      } catch (err) {
        console.error("Ошибка при отправке broadcast:", err)
      }
    } catch (err: any) {
      toast({
        title: "Ошибка",
        description: err.message,
        variant: "destructive",
      })
    }
  }

  // Remove a tag from a task
  const handleRemoveTagFromTask = async (taskId: string, tagToRemove: string) => {
    try {
      const { error } = await removeTagFromTask(taskId, tagToRemove)

      if (error) {
        throw new Error(error)
      }

      setTasks(
        tasks.map((task) => {
          if (task.id === taskId) {
            return { ...task, tags: task.tags.filter((tag) => tag !== tagToRemove) }
          }
          return task
        }),
      )

      toast({
        title: "Тег удален",
        description: `Тег "${tagToRemove}" удален из задачи`,
      })

      // Уведомляем другие вкладки об изменении
      try {
        if (channel && typeof channel.send === "function") {
          await channel.send({
            type: "broadcast",
            event: "tasks-update",
            payload: { type: "tag", id: taskId },
          })
        }
      } catch (err) {
        console.error("Ошибка при отправке broadcast:", err)
      }
    } catch (err: any) {
      toast({
        title: "Ошибка",
        description: err.message,
        variant: "destructive",
      })
    }
  }

  // Open task dialog
  const openTaskDialog = (task: Task) => {
    setSelectedTask(task)
    setTaskComment(task.comment)
    setIsTaskDialogOpen(true)
  }

  // Save task comment
  const saveTaskComment = async () => {
    if (!selectedTask) return

    try {
      const { task, error } = await updateTask(selectedTask.id, { comment: taskComment })

      if (error) {
        throw new Error(error)
      }

      setTasks(tasks.map((t) => (t.id === selectedTask.id ? { ...t, comment: taskComment } : t)))

      toast({
        title: "Комментарий сохранен",
        description: "Комментарий к задаче успешно сохранен",
      })

      // Уведомляем другие вкладки об изменении
      try {
        if (channel && typeof channel.send === "function") {
          await channel.send({
            type: "broadcast",
            event: "tasks-update",
            payload: { type: "update", id: selectedTask.id },
          })
        }
      } catch (err) {
        console.error("Ошибка при отправке broadcast:", err)
      }
    } catch (err: any) {
      toast({
        title: "Ошибка",
        description: err.message,
        variant: "destructive",
      })
    }
  }

  // Update task status
  const updateTaskStatus = async (taskId: string, status: "pending" | "completed" | "cancelled") => {
    try {
      const completed = status === "completed"
      const { task, error } = await updateTask(taskId, { status, completed })

      if (error) {
        throw new Error(error)
      }

      setTasks(
        tasks.map((t) =>
          t.id === taskId
            ? {
                ...t,
                status,
                completed,
              }
            : t,
        ),
      )

      setIsTaskDialogOpen(false)

      toast({
        title: "Статус обновлен",
        description: `Задача отмечена как ${
          status === "completed" ? "выполненная" : status === "cancelled" ? "отмененная" : "активная"
        }`,
      })

      // Уведомляем другие вкладки об изменении
      try {
        if (channel && typeof channel.send === "function") {
          await channel.send({
            type: "broadcast",
            event: "tasks-update",
            payload: { type: "update", id: taskId },
          })
        }
      } catch (err) {
        console.error("Ошибка при отправке broadcast:", err)
      }
    } catch (err: any) {
      toast({
        title: "Ошибка",
        description: err.message,
        variant: "destructive",
      })
    }
  }

  // Close dialog
  const closeTaskDialog = () => {
    saveTaskComment()
    setIsTaskDialogOpen(false)
  }

  // Handle touch start for swipe gestures
  const handleTouchStart = (e: React.TouchEvent, taskId: string) => {
    touchStartX.current = e.touches[0].clientX
    touchStartY.current = e.touches[0].clientY
    currentTaskRef.current = taskId
  }

  // Handle touch end for swipe gestures
  const handleTouchEnd = (e: React.TouchEvent) => {
    if (touchStartX.current === null || touchStartY.current === null || !currentTaskRef.current) return

    const touchEndX = e.changedTouches[0].clientX
    const touchEndY = e.changedTouches[0].clientY
    const deltaX = touchEndX - touchStartX.current
    const deltaY = touchEndY - touchStartY.current

    // Check if the swipe was more horizontal than vertical
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
      if (deltaX > 0) {
        // Swipe right - mark as completed
        const task = tasks.find((t) => t.id === currentTaskRef.current)
        if (task) {
          handleToggleTaskCompletion(currentTaskRef.current, !task.completed)
        }
      } else {
        // Swipe left - delete task
        handleRemoveTask(currentTaskRef.current)
      }
    }

    touchStartX.current = null
    touchStartY.current = null
    currentTaskRef.current = null
  }

  // Render task dialog based on device type
  const TaskDetailsComponent = () => {
    if (!selectedTask) return null

    const content = (
      <>
        <div className="flex flex-wrap gap-2">
          {selectedTask.tags.map((tag) => (
            <Badge key={tag} variant="secondary">
              {tag}
            </Badge>
          ))}
        </div>

        <div className="grid gap-2">
          <label htmlFor="comment" className="text-sm font-medium">
            Комментарий
          </label>
          <textarea
            id="comment"
            rows={4}
            className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            placeholder="Добавьте комментарий к задаче..."
            value={taskComment}
            onChange={(e) => setTaskComment(e.target.value)}
          />
        </div>

        <div className="border-t pt-4">
          <div className="flex justify-between items-center">
            <div className="text-sm font-medium">Статус задачи</div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => updateTaskStatus(selectedTask.id, "completed")}
                className="text-green-600 hover:text-green-700 hover:bg-green-50"
                title="Отметить как выполненную"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-check"
                >
                  <path d="M20 6 9 17l-5-5" />
                </svg>
              </Button>

              <Button
                variant="outline"
                size="icon"
                onClick={() => updateTaskStatus(selectedTask.id, "cancelled")}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                title="Отменить задачу"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-x"
                >
                  <path d="M18 6 6 18" />
                  <path d="m6 6 12 12" />
                </svg>
              </Button>

              <Button variant="outline" size="icon" onClick={closeTaskDialog} title="Закрыть">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-x-circle"
                >
                  <circle cx="12" cy="12" r="10" />
                  <path d="m15 9-6 6" />
                  <path d="m9 9 6 6" />
                </svg>
              </Button>
            </div>
          </div>
        </div>
      </>
    )

    if (isMobile) {
      return (
        <Drawer open={isTaskDialogOpen} onOpenChange={setIsTaskDialogOpen}>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>{selectedTask.text}</DrawerTitle>
            </DrawerHeader>
            <div className="p-4 grid gap-4">{content}</div>
          </DrawerContent>
        </Drawer>
      )
    }

    return (
      <Dialog open={isTaskDialogOpen} onOpenChange={setIsTaskDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{selectedTask.text}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">{content}</div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <div className="flex flex-col min-h-[100dvh] bg-background">
      <header className="sticky top-0 z-10 bg-background border-b px-4 h-14 flex items-center justify-between">
        <h1 className="text-xl font-bold">Задачи</h1>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" onClick={refreshTasks} disabled={isRefreshing} className="relative">
            <RefreshCw className={`h-5 w-5 ${isRefreshing ? "animate-spin" : ""}`} />
            <span className="sr-only">Обновить</span>
          </Button>
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Меню</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <SheetHeader>
                <SheetTitle>Настройки</SheetTitle>
              </SheetHeader>
              <div className="py-4">
                <div className="flex items-center justify-between">
                  <span>Пользователь</span>
                  <span className="text-muted-foreground">{user?.email}</span>
                </div>
                <div className="mt-4">
                  <Button variant="outline" className="w-full" onClick={() => signOut()}>
                    <LogOut className="h-4 w-4 mr-2" />
                    Выйти
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </header>

      <main className="flex-1 container max-w-3xl p-4">
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {realtimeError && (
          <Alert variant="warning" className="mb-4">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertDescription>
              Проблема с обновлениями в реальном времени. Некоторые изменения могут отображаться с задержкой.
            </AlertDescription>
          </Alert>
        )}

        <div className="flex items-center w-full space-x-2 mb-6">
          <Input
            type="text"
            placeholder="Добавить новую задачу..."
            value={newTask}
            onChange={(e) => setNewTask(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleAddTask()
              }
            }}
            className="flex-1"
            disabled={isLoading}
          />
          <Button onClick={handleAddTask} disabled={isLoading}>
            <Plus className="h-4 w-4 mr-1" />
            <span className="sm:inline hidden">Добавить</span>
          </Button>
        </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4 flex flex-wrap overflow-x-auto pb-1 scrollbar-hide">
            <TabsTrigger value="all">Все</TabsTrigger>
            <TabsTrigger value="completed">Выполненные</TabsTrigger>
            {allTags.map((tag) => (
              <TabsTrigger key={tag} value={tag}>
                {tag}
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value={activeTab} className="space-y-4">
            {filteredTasks.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">Нет задач в этой категории</div>
            ) : (
              filteredTasks.map((task) => (
                <div
                  key={task.id}
                  className={`flex flex-col rounded-lg border p-4 shadow-sm transition-shadow hover:shadow-md active:bg-muted/30 ${
                    task.status === "completed" ? "bg-muted/50" : task.status === "cancelled" ? "bg-red-100/30" : ""
                  }`}
                  onClick={() => openTaskDialog(task)}
                  onTouchStart={(e) => handleTouchStart(e, task.id)}
                  onTouchEnd={handleTouchEnd}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`task-${task.id}`}
                        checked={task.completed}
                        onCheckedChange={(checked) => {
                          handleToggleTaskCompletion(task.id, checked === true)
                          return false
                        }}
                        onClick={(e) => e.stopPropagation()}
                        className="touch-manipulation"
                      />
                      <label
                        htmlFor={`task-${task.id}`}
                        className={`font-medium ${
                          task.status === "completed"
                            ? "line-through text-muted-foreground"
                            : task.status === "cancelled"
                              ? "line-through text-red-500"
                              : ""
                        }`}
                      >
                        {task.text}
                      </label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation()
                              setEditingTaskId(task.id)
                            }}
                            aria-label="Добавить тег"
                            className="touch-manipulation"
                          >
                            <Tag className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent onClick={(e) => e.stopPropagation()}>
                          <DialogHeader>
                            <DialogTitle>Добавить тег к задаче</DialogTitle>
                          </DialogHeader>
                          <div className="flex items-center space-x-2 mt-4">
                            <Input
                              type="text"
                              placeholder="Новый тег..."
                              value={newTag}
                              onChange={(e) => setNewTag(e.target.value)}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  handleAddTagToTask(task.id)
                                }
                              }}
                            />
                            <Button onClick={() => handleAddTagToTask(task.id)}>Добавить</Button>
                          </div>
                        </DialogContent>
                      </Dialog>

                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleRemoveTask(task.id)
                        }}
                        aria-label="Удалить задачу"
                        className="touch-manipulation"
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {task.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-3">
                      {task.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                          {tag}
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleRemoveTagFromTask(task.id, tag)
                            }}
                            className="ml-1 rounded-full hover:bg-muted p-0.5 touch-manipulation"
                            aria-label={`Удалить тег ${tag}`}
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  )}

                  {task.comment && (
                    <div className="mt-2 text-sm text-muted-foreground">
                      <p className="line-clamp-1">{task.comment}</p>
                    </div>
                  )}
                </div>
              ))
            )}
          </TabsContent>
        </Tabs>
      </main>

      <footer className="sticky bottom-0 z-10 bg-background border-t px-4 py-2 flex justify-center">
        <div className="text-xs text-muted-foreground">
          Задач: {tasks.length} • Выполнено: {tasks.filter((t) => t.completed).length}
        </div>
      </footer>

      <TaskDetailsComponent />
    </div>
  )
}
