"use client"

import { useRef, useState, useEffect } from "react"
import { createClient } from "@/lib/supabase/client"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ReportFilters } from "../report-filters"
import { PrintReportButton } from "../print-report-button"
import { format } from "date-fns"
import { Loader2 } from "lucide-react"

export default function ClientReportPage() {
  const [loading, setLoading] = useState(true)
  const [reportData, setReportData] = useState<any[]>([])
  const [clients, setClients] = useState<any[]>([])
  const [intervals, setIntervals] = useState<any[]>([])
  const [groups, setGroups] = useState<any[]>([])
  const [filters, setFilters] = useState<any>({})
  const reportRef = useRef<HTMLDivElement>(null)
  const supabase = createClient()

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)

      // Загружаем клиентов
      const { data: clientsData } = await supabase.from("clients").select("id, full_name").order("full_name")
      if (clientsData) setClients(clientsData)

      // Загружаем интервалы
      const { data: intervalsData } = await supabase.from("task_intervals").select("id, name").order("name")
      if (intervalsData) setIntervals(intervalsData)

      // Загружаем группы
      const { data: groupsData } = await supabase.from("task_groups").select("id, name").order("name")
      if (groupsData) setGroups(groupsData)

      // Загружаем данные отчета
      await fetchReportData(filters)
    }

    fetchData()
  }, [])

  const fetchReportData = async (filters: any) => {
    setLoading(true)

    let query = supabase
      .from("tasks")
      .select(
        `
        *,
        client:client_id(id, full_name),
        interval:interval_id(id, name),
        group:group_id(id, name)
      `,
      )
      .order("execution_date", { ascending: false })

    // Применяем фильтры
    if (filters.client_id) {
      query = query.eq("client_id", filters.client_id)
    }

    if (filters.interval_id) {
      query = query.eq("interval_id", filters.interval_id)
    }

    if (filters.group_id) {
      query = query.eq("group_id", filters.group_id)
    }

    if (filters.status) {
      query = query.eq("status", filters.status)
    }

    if (filters.start_date) {
      const startDate = format(filters.start_date, "yyyy-MM-dd")
      query = query.gte("execution_date", startDate)
    }

    if (filters.end_date) {
      const endDate = format(filters.end_date, "yyyy-MM-dd")
      query = query.lte("execution_date", endDate)
    }

    const { data, error } = await query

    if (error) {
      console.error("Error fetching tasks:", error)
      setReportData([])
    } else {
      // Группируем задачи по клиентам
      const groupedByClient: Record<string, any> = {}

      data?.forEach((task) => {
        const clientId = task.client_id || "no_client"
        const clientName = task.client?.full_name || "Без клиента"

        if (!groupedByClient[clientId]) {
          groupedByClient[clientId] = {
            id: clientId,
            name: clientName,
            tasks: [],
            total: 0,
            completed: 0,
            pending: 0,
            in_progress: 0,
            cancelled: 0,
          }
        }

        groupedByClient[clientId].tasks.push(task)
        groupedByClient[clientId].total++

        switch (task.status) {
          case "completed":
            groupedByClient[clientId].completed++
            break
          case "pending":
            groupedByClient[clientId].pending++
            break
          case "in_progress":
            groupedByClient[clientId].in_progress++
            break
          case "cancelled":
            groupedByClient[clientId].cancelled++
            break
        }
      })

      // Преобразуем объект в массив и сортируем по имени клиента
      const result = Object.values(groupedByClient).sort((a: any, b: any) => a.name.localeCompare(b.name))
      setReportData(result)
    }

    setLoading(false)
  }

  const handleApplyFilters = (newFilters: any) => {
    setFilters(newFilters)
    fetchReportData(newFilters)
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Отчет по клиентам</h1>
        <PrintReportButton reportRef={reportRef} title="Отчет по клиентам" />
      </div>

      <ReportFilters
        clients={clients}
        intervals={intervals}
        groups={groups}
        onApplyFilters={handleApplyFilters}
        showClientFilter={false}
      />

      <div ref={reportRef}>
        <Card>
          <CardHeader className="print-only" style={{ display: "none" }}>
            <CardTitle>Отчет по клиентам</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : reportData.length === 0 ? (
              <p className="text-center py-8 text-muted-foreground">Нет данных для отображения</p>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Клиент</TableHead>
                    <TableHead className="text-right">Всего задач</TableHead>
                    <TableHead className="text-right">Выполнено</TableHead>
                    <TableHead className="text-right">В процессе</TableHead>
                    <TableHead className="text-right">Ожидает</TableHead>
                    <TableHead className="text-right">Отменено</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reportData.map((client) => (
                    <TableRow key={client.id}>
                      <TableCell className="font-medium">{client.name}</TableCell>
                      <TableCell className="text-right">{client.total}</TableCell>
                      <TableCell className="text-right">{client.completed}</TableCell>
                      <TableCell className="text-right">{client.in_progress}</TableCell>
                      <TableCell className="text-right">{client.pending}</TableCell>
                      <TableCell className="text-right">{client.cancelled}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
