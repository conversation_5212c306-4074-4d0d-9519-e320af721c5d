"use client"

import { useRef, useState, useEffect } from "react"
import { createClient } from "@/lib/supabase/client"
import { format } from "date-fns"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DatePicker } from "@/components/ui/date-picker"
import { Loader2, Printer } from "lucide-react"

export default function StatusReportPage() {
  const [loading, setLoading] = useState(true)
  const [reportData, setReportData] = useState<any>({})
  const [clients, setClients] = useState<any[]>([])
  const [intervals, setIntervals] = useState<any[]>([])
  const [groups, setGroups] = useState<any[]>([])
  const [filters, setFilters] = useState<any>({
    client_id: null,
    interval_id: null,
    group_id: null,
    status: null,
    start_date: null,
    end_date: null,
  })
  const reportRef = useRef<HTMLDivElement>(null)
  const supabase = createClient()

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)

      // Загружаем клиентов
      const { data: clientsData } = await supabase.from("clients").select("id, full_name").order("full_name")
      if (clientsData) setClients(clientsData)

      // Загружаем интервалы
      const { data: intervalsData } = await supabase.from("task_intervals").select("id, name").order("name")
      if (intervalsData) setIntervals(intervalsData)

      // Загружаем группы
      const { data: groupsData } = await supabase.from("task_groups").select("id, name").order("name")
      if (groupsData) setGroups(groupsData)

      // Загружаем данные отчета
      await fetchReportData(filters)
    }

    fetchData()
  }, [])

  const fetchReportData = async (filters: any) => {
    setLoading(true)

    let query = supabase
      .from("tasks")
      .select(
        `
        *,
        client:client_id(id, full_name),
        interval:interval_id(id, name),
        group:group_id(id, name)
      `,
      )
      .order("execution_date", { ascending: false })

    // Применяем фильтры
    if (filters.client_id) {
      query = query.eq("client_id", filters.client_id)
    }

    if (filters.interval_id) {
      query = query.eq("interval_id", filters.interval_id)
    }

    if (filters.group_id) {
      query = query.eq("group_id", filters.group_id)
    }

    if (filters.status) {
      query = query.eq("status", filters.status)
    }

    if (filters.start_date) {
      const startDate = format(filters.start_date, "yyyy-MM-dd")
      query = query.gte("execution_date", startDate)
    }

    if (filters.end_date) {
      const endDate = format(filters.end_date, "yyyy-MM-dd")
      query = query.lte("execution_date", endDate)
    }

    const { data, error } = await query

    if (error) {
      console.error("Error fetching tasks:", error)
      setReportData({})
    } else {
      // Группируем задачи по статусам
      const statusCounts = {
        pending: 0,
        in_progress: 0,
        completed: 0,
        cancelled: 0,
        total: 0,
      }

      // Группируем задачи по дням
      const byDate: Record<string, any> = {}

      data?.forEach((task) => {
        // Увеличиваем счетчики по статусам
        statusCounts.total++
        switch (task.status) {
          case "pending":
            statusCounts.pending++
            break
          case "in_progress":
            statusCounts.in_progress++
            break
          case "completed":
            statusCounts.completed++
            break
          case "cancelled":
            statusCounts.cancelled++
            break
        }

        // Группируем по дате
        if (task.execution_date) {
          const dateKey = task.execution_date.split("T")[0]
          if (!byDate[dateKey]) {
            byDate[dateKey] = {
              date: dateKey,
              pending: 0,
              in_progress: 0,
              completed: 0,
              cancelled: 0,
              total: 0,
            }
          }

          byDate[dateKey].total++
          switch (task.status) {
            case "pending":
              byDate[dateKey].pending++
              break
            case "in_progress":
              byDate[dateKey].in_progress++
              break
            case "completed":
              byDate[dateKey].completed++
              break
            case "cancelled":
              byDate[dateKey].cancelled++
              break
          }
        }
      })

      // Сортируем даты
      const sortedDates = Object.values(byDate).sort((a, b) => {
        return new Date(b.date).getTime() - new Date(a.date).getTime()
      })

      setReportData({
        tasks: data || [],
        statusCounts,
        byDate: sortedDates,
      })
    }

    setLoading(false)
  }

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    fetchReportData(newFilters)
  }

  const handlePrint = () => {
    if (reportRef.current) {
      const printWindow = window.open("", "_blank")
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>Отчет по статусам задач</title>
              <style>
                body { font-family: Arial, sans-serif; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .header { margin-bottom: 20px; }
                .summary { margin-bottom: 20px; }
              </style>
            </head>
            <body>
              ${reportRef.current.innerHTML}
            </body>
          </html>
        `)
        printWindow.document.close()
        printWindow.focus()
        printWindow.print()
      }
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "Ожидает"
      case "in_progress":
        return "В процессе"
      case "completed":
        return "Выполнено"
      case "cancelled":
        return "Отменено"
      default:
        return status
    }
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Отчет по статусам задач</h1>
        <div className="flex gap-2">
          <Button onClick={handlePrint} variant="outline" className="flex items-center gap-2">
            <Printer className="h-4 w-4" />
            Печать
          </Button>
        </div>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Фильтры</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Клиент</label>
              <Select
                value={filters.client_id || "all"}
                onValueChange={(value) => handleFilterChange("client_id", value === "all" ? null : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Все клиенты" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Все клиенты</SelectItem>
                  {clients.map((client) => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.full_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Интервал</label>
              <Select
                value={filters.interval_id || "all"}
                onValueChange={(value) => handleFilterChange("interval_id", value === "all" ? null : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Все интервалы" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Все интервалы</SelectItem>
                  {intervals.map((interval) => (
                    <SelectItem key={interval.id} value={interval.id}>
                      {interval.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Группа</label>
              <Select
                value={filters.group_id || "all"}
                onValueChange={(value) => handleFilterChange("group_id", value === "all" ? null : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Все группы" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Все группы</SelectItem>
                  {groups.map((group) => (
                    <SelectItem key={group.id} value={group.id}>
                      {group.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Статус</label>
              <Select
                value={filters.status || "all"}
                onValueChange={(value) => handleFilterChange("status", value === "all" ? null : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Все статусы" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Все статусы</SelectItem>
                  <SelectItem value="pending">Ожидает</SelectItem>
                  <SelectItem value="in_progress">В процессе</SelectItem>
                  <SelectItem value="completed">Выполнено</SelectItem>
                  <SelectItem value="cancelled">Отменено</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Дата начала</label>
              <DatePicker
                date={filters.start_date}
                setDate={(date) => handleFilterChange("start_date", date)}
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Дата окончания</label>
              <DatePicker
                date={filters.end_date}
                setDate={(date) => handleFilterChange("end_date", date)}
                className="w-full"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <div ref={reportRef}>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Сводка по статусам</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="bg-gray-100 p-4 rounded-lg">
                  <div className="text-sm text-gray-500">Всего задач</div>
                  <div className="text-2xl font-bold">{reportData.statusCounts?.total || 0}</div>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <div className="text-sm text-yellow-500">Ожидает</div>
                  <div className="text-2xl font-bold">{reportData.statusCounts?.pending || 0}</div>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-sm text-blue-500">В процессе</div>
                  <div className="text-2xl font-bold">{reportData.statusCounts?.in_progress || 0}</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-sm text-green-500">Выполнено</div>
                  <div className="text-2xl font-bold">{reportData.statusCounts?.completed || 0}</div>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="text-sm text-red-500">Отменено</div>
                  <div className="text-2xl font-bold">{reportData.statusCounts?.cancelled || 0}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Статистика по дням</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr>
                      <th className="text-left p-2">Дата</th>
                      <th className="text-left p-2">Всего</th>
                      <th className="text-left p-2">Ожидает</th>
                      <th className="text-left p-2">В процессе</th>
                      <th className="text-left p-2">Выполнено</th>
                      <th className="text-left p-2">Отменено</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.byDate?.map((day: any) => (
                      <tr key={day.date}>
                        <td className="border-t p-2">{format(new Date(day.date), "dd.MM.yyyy")}</td>
                        <td className="border-t p-2">{day.total}</td>
                        <td className="border-t p-2">{day.pending}</td>
                        <td className="border-t p-2">{day.in_progress}</td>
                        <td className="border-t p-2">{day.completed}</td>
                        <td className="border-t p-2">{day.cancelled}</td>
                      </tr>
                    ))}
                    {reportData.byDate?.length === 0 && (
                      <tr>
                        <td colSpan={6} className="text-center py-4">
                          Нет данных для отображения
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
