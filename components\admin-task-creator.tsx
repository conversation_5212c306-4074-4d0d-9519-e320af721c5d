"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Calendar } from "@/components/ui/calendar"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { format, addDays } from "date-fns"
import { ru } from "date-fns/locale"
import { useToast } from "@/hooks/use-toast"
import { createClient } from "@/lib/supabase/client"
import { Loader2, Check, Plus, Trash, Edit, User, RotateCw } from "lucide-react"
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>oot<PERSON> } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useAuth } from "@/components/auth-provider"
import { Badge } from "@/components/ui/badge"

// Initial task templates structure
const initialTaskTemplates = {
  hygiene: [
    { text: "Умывание", interval_id: "morning", time: "07:00" },
    { text: "Чистка зубов", interval_id: "morning", time: "07:30" },
    { text: "Бритье", interval_id: null, time: null },
    { text: "Уход за волосами", interval_id: null, time: null },
    { text: "Уход за ногтями", interval_id: null, time: null },
    { text: "Переодевание", interval_id: null, time: null },
    { text: "Купание", interval_id: null, time: null },
    { text: "Подмывание", interval_id: null, time: null },
    { text: "Смена пост. белья", interval_id: null, time: null },
    { text: "Умывание", interval_id: "evening", time: "20:00" },
    { text: "Чистка зубов", interval_id: "evening", time: "20:30" },
  ],
  observations: [
    { text: "АД", interval_id: "morning", time: "08:00" },
    { text: "ЧСС", interval_id: "morning", time: "08:00" },
    { text: "Глюкоза крови натощак", interval_id: "morning", time: "07:30" },
    { text: "Сатурация", interval_id: null, time: null },
    { text: "Отеки", interval_id: null, time: null },
    { text: "Кожные покровы", interval_id: null, time: null },
    { text: "Дыхание", interval_id: null, time: null },
    { text: "Глюкоза крови после завтрака", interval_id: null, time: null },
    { text: "Стул", interval_id: null, time: null },
    { text: "Мочеиспускание", interval_id: null, time: null },
    { text: "Глюкоза крови вечером", interval_id: "evening", time: "19:00" },
    { text: "Температура тела", interval_id: null, time: null },
    { text: "АД", interval_id: "evening", time: "19:30" },
    { text: "ЧСС", interval_id: "evening", time: "19:30" },
  ],
  medications: [
    { text: "Утро натощак", interval_id: "morning", time: "07:00" },
    { text: "Утро", interval_id: "morning", time: "08:00" },
    { text: "День", interval_id: "day", time: "13:00" },
    { text: "Вечер", interval_id: "evening", time: "18:00" },
    { text: "На ночь", interval_id: "evening", time: "21:00" },
    { text: "Дополнительный прием 1", interval_id: null, time: null },
    { text: "Дополнительный прием 2", interval_id: null, time: null },
  ],
  nutrition: [
    { text: "Завтрак", interval_id: "morning", time: "08:30" },
    { text: "Обед", interval_id: "day", time: "13:00" },
    { text: "Полдник", interval_id: "day", time: "16:00" },
    { text: "Ужин", interval_id: "evening", time: "19:00" },
    { text: "Дополнительный прием 1", interval_id: null, time: null },
    { text: "Дополнительный прием 2", interval_id: null, time: null },
    { text: "Объем выпитой жидкости", interval_id: null, time: null },
    { text: "Ккал за сутки", interval_id: null, time: null },
  ],
  activities: [
    { text: "Гимнастика", interval_id: null, time: null },
    { text: "Прогулка", interval_id: null, time: null },
    { text: "Сон дневной", interval_id: null, time: null },
    { text: "Досуг", interval_id: null, time: null },
    { text: "Сон ночной", interval_id: null, time: null },
  ],
  cleaning: [
    { text: "Сухая уборка помещения", interval_id: null, time: null },
    { text: "Влажная уборка помещения", interval_id: null, time: null },
    { text: "Стирка вещей", interval_id: null, time: null },
    { text: "Глаженье вещей", interval_id: null, time: null },
    { text: "Уход за обувью", interval_id: null, time: null },
  ],
}

// Группы задач
const taskGroups = {
  hygiene: "shower", // Иконка для группы "Гигиена"
  observations: "eye", // Иконка для группы "Наблюдения"
  medications: "pill", // Иконка для группы "Прием лекарств"
  nutrition: "utensils", // Иконка для группы "Питание"
  activities: "activity", // Иконка для группы "Активности"
  cleaning: "trash", // Иконка для группы "Уборка"
}

interface IAdminTaskCreatorUser {
  id: string
  email: string
}

interface AdminTaskCreatorProps {
  users: IAdminTaskCreatorUser[]
}

export function AdminTaskCreator({ users }: AdminTaskCreatorProps) {
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [userIdsInput, setUserIdsInput] = useState("")
  const [startDate, setStartDate] = useState<Date>(new Date())
  const [endDate, setEndDate] = useState<Date>(addDays(new Date(), 7))
  const [selectedTemplates, setSelectedTemplates] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [intervals, setIntervals] = useState<any[]>([])
  const { toast } = useToast()
  const supabase = createClient()
  const { user } = useAuth()

  // State for editable task templates
  const [taskTemplates, setTaskTemplates] = useState(initialTaskTemplates)
  const [activeEditTemplate, setActiveEditTemplate] = useState<string | null>(null)
  const [editTaskDialog, setEditTaskDialog] = useState(false)
  const [currentEditTask, setCurrentEditTask] = useState<{
    templateName: string
    index: number
    task: { text: string; interval_id: string | null; time: string | null }
  } | null>(null)

  // Fetch intervals when component mounts
  useEffect(() => {
    const fetchIntervals = async () => {
      try {
        const { data } = await supabase.from("task_intervals").select("*")
        if (data) {
          setIntervals(data)
        }
      } catch (err) {
        console.error("Error fetching intervals:", err)
      }
    }

    fetchIntervals()
  }, [supabase])

  // Function to add current user ID to input field
  const addCurrentUserToList = () => {
    if (user && user.id) {
      if (userIdsInput.trim()) {
        setUserIdsInput((prev) => `${prev}, ${user.id}`)
      } else {
        setUserIdsInput(user.id)
      }
      toast({
        title: "ID пользователя добавлен",
        description: "Ваш ID добавлен в список пользователей",
      })
    }
  }

  // Function to randomize task times and intervals
  const randomizeTasksTimesAndIntervals = (templateName: string) => {
    if (!intervals.length) {
      toast({
        title: "Ошибка",
        description: "Не удалось получить интервалы для рандомизации",
        variant: "destructive",
      })
      return
    }

    setTaskTemplates((prev) => {
      const template = [...prev[templateName as keyof typeof prev]]

      template.forEach((task) => {
        // Randomly assign interval
        const randomInterval = Math.random() > 0.3 ? intervals[Math.floor(Math.random() * intervals.length)].name : null

        // Randomly assign time
        const randomHour = Math.floor(Math.random() * 24)
        const randomMinute = Math.random() > 0.5 ? 0 : 30
        const randomTime =
          randomHour > 0 && Math.random() > 0.3
            ? `${randomHour.toString().padStart(2, "0")}:${randomMinute.toString().padStart(2, "0")}`
            : null

        task.interval_id = randomInterval
        task.time = randomTime
      })

      return { ...prev, [templateName]: template }
    })

    toast({
      title: "Рандомизация выполнена",
      description: "Интервалы и время задач были случайно распределены",
    })
  }

  // Function to edit a task
  const editTask = (templateName: string, index: number) => {
    setCurrentEditTask({
      templateName,
      index,
      task: { ...taskTemplates[templateName as keyof typeof taskTemplates][index] },
    })
    setEditTaskDialog(true)
  }

  // Function to add a new task to a template
  const addNewTask = (templateName: string) => {
    setCurrentEditTask({
      templateName,
      index: -1, // -1 indicates a new task
      task: { text: "", interval_id: null, time: null },
    })
    setEditTaskDialog(true)
  }

  // Function to save edited task
  const saveTaskEdit = () => {
    if (!currentEditTask) return

    const { templateName, index, task } = currentEditTask

    setTaskTemplates((prev) => {
      const template = [...prev[templateName as keyof typeof prev]]

      // Add new task or update existing
      if (index === -1) {
        template.push(task)
      } else {
        template[index] = task
      }

      return { ...prev, [templateName]: template }
    })

    setEditTaskDialog(false)
    setCurrentEditTask(null)

    toast({
      title: index === -1 ? "Задача добавлена" : "Задача обновлена",
      description: "Изменения сохранены успешно",
    })
  }

  // Function to delete a task
  const deleteTask = (templateName: string, index: number) => {
    setTaskTemplates((prev) => {
      const template = [...prev[templateName as keyof typeof prev]]
      template.splice(index, 1)
      return { ...prev, [templateName]: template }
    })

    toast({
      title: "Задача удалена",
      description: "Задача успешно удалена из шаблона",
    })
  }

  // Обработчик выбора пользователя
  const handleUserSelect = (userId: string) => {
    setSelectedUsers((prev) => (prev.includes(userId) ? prev.filter((id) => id !== userId) : [...prev, userId]))
  }

  // Обработчик выбора шаблона
  const handleTemplateSelect = (templateName: string) => {
    setSelectedTemplates((prev) =>
      prev.includes(templateName) ? prev.filter((name) => name !== templateName) : [...prev, templateName],
    )
  }

  // Обработчик ввода ID пользователей
  const handleUserIdsInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setUserIdsInput(e.target.value)
  }

  // Парсинг ID пользователей из текстового поля
  const parseUserIds = () => {
    if (!userIdsInput.trim()) return []

    // Разделяем строку по запятым, пробелам или переносам строк
    return userIdsInput
      .split(/[,\s]+/)
      .map((id) => id.trim())
      .filter((id) => id.length > 0)
  }

  // Создание задач для выбранных пользователей
  const createTasks = async () => {
    // Объединяем выбранных пользователей из списка и из текстового поля
    const userIds = [...selectedUsers, ...parseUserIds()]

    if (userIds.length === 0) {
      toast({
        title: "Ошибка",
        description: "Выберите хотя бы одного пользователя или введите ID пользователей",
        variant: "destructive",
      })
      return
    }

    if (selectedTemplates.length === 0) {
      toast({
        title: "Ошибка",
        description: "Выберите хотя бы один шаблон задач",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    setError(null)
    setSuccess(false)

    try {
      // Получаем интервалы
      const { data: intervals } = await supabase.from("task_intervals").select("id, name")

      // Получаем группы
      const { data: groups } = await supabase.from("task_groups").select("id, name, icon")

      // Создаем массив задач для вставки
      const tasksToInsert = []

      // Перебираем даты в выбранном диапазоне
      let currentDate = new Date(startDate)
      while (currentDate <= endDate) {
        const formattedDate = format(currentDate, "yyyy-MM-dd")

        // Перебираем выбранных пользователей
        for (const userId of userIds) {
          // Перебираем выбранные шаблоны
          for (const templateName of selectedTemplates) {
            const tasks = taskTemplates[templateName as keyof typeof taskTemplates]
            const groupIcon = taskGroups[templateName as keyof typeof taskGroups]

            // Находим ID группы по иконке
            const group = groups?.find((g) => g.icon === groupIcon)

            // Перебираем задачи в шаблоне
            for (const task of tasks) {
              // Находим ID интервала по имени
              let intervalId = task.interval_id
              if (intervalId) {
                const interval = intervals?.find((i) => i.name.toLowerCase() === intervalId)
                intervalId = interval?.id || null
              }

              tasksToInsert.push({
                user_id: userId,
                text: task.text,
                status: "pending",
                completed: false,
                execution_date: formattedDate,
                execution_time: task.time,
                interval_id: intervalId,
                group_id: group?.id || null,
                created_by: null, // Задача создается системой
              })
            }
          }
        }

        // Переходим к следующей дате
        currentDate = addDays(currentDate, 1)
      }

      // Вставляем задачи в базу данных
      const { error: insertError } = await supabase.from("tasks").insert(tasksToInsert)

      if (insertError) {
        throw new Error(insertError.message)
      }

      setSuccess(true)
      toast({
        title: "Успех",
        description: `Создано ${tasksToInsert.length} задач для ${userIds.length} пользователей`,
      })
    } catch (err: any) {
      console.error("Ошибка при создании задач:", err)
      setError(err.message || "Не удалось создать задачи")
      toast({
        title: "Ошибка",
        description: err.message || "Не удалось создать задачи",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Создание задач для пользователей</CardTitle>
        <CardDescription>Выберите пользователей, период и шаблоны задач</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="users" className="space-y-4">
          <TabsList>
            <TabsTrigger value="users">Пользователи</TabsTrigger>
            <TabsTrigger value="dates">Период</TabsTrigger>
            <TabsTrigger value="templates">Шаблоны</TabsTrigger>
          </TabsList>

          <TabsContent value="users" className="space-y-4">
            <div className="grid gap-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Выберите пользователей</h3>
                {users && users.length > 0 ? (
                  <div className="grid gap-2 max-h-80 overflow-y-auto">
                    {users.map((user) => (
                      <div key={user.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`user-${user.id}`}
                          checked={selectedUsers.includes(user.id)}
                          onCheckedChange={() => handleUserSelect(user.id)}
                        />
                        <label
                          htmlFor={`user-${user.id}`}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {user.email}
                        </label>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Alert className="mb-4">
                    <AlertDescription>
                      Не удалось загрузить список пользователей. Пожалуйста, введите ID пользователей вручную.
                    </AlertDescription>
                  </Alert>
                )}
                <div className="text-sm text-muted-foreground mt-2">Выбрано пользователей: {selectedUsers.length}</div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-lg font-medium">Или введите ID пользователей</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={addCurrentUserToList}
                    className="flex items-center gap-1"
                  >
                    <User className="h-4 w-4" />
                    <span>Добавить свой ID</span>
                  </Button>
                </div>
                <Textarea
                  placeholder="Введите ID пользователей через запятую или каждый с новой строки"
                  value={userIdsInput}
                  onChange={handleUserIdsInputChange}
                  className="min-h-[100px]"
                />
                <div className="text-sm text-muted-foreground mt-2">
                  Введено ID пользователей: {parseUserIds().length}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="dates" className="space-y-4">
            <div className="grid gap-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Дата начала</h3>
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={(date) => date && setStartDate(date)}
                  className="rounded-md border"
                />
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">Дата окончания</h3>
                <Calendar
                  mode="single"
                  selected={endDate}
                  onSelect={(date) => date && setEndDate(date)}
                  className="rounded-md border"
                />
              </div>
              <div className="text-sm text-muted-foreground">
                Период: с {format(startDate, "d MMMM yyyy", { locale: ru })} по{" "}
                {format(endDate, "d MMMM yyyy", { locale: ru })}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            <div className="grid gap-3">
              <h3 className="text-lg font-medium">Выберите шаблоны задач</h3>
              <div className="grid gap-2">
                {Object.keys(taskTemplates).map((templateName) => (
                  <div key={templateName} className="flex items-center space-x-2">
                    <Checkbox
                      id={`template-${templateName}`}
                      checked={selectedTemplates.includes(templateName)}
                      onCheckedChange={() => handleTemplateSelect(templateName)}
                    />
                    <label
                      htmlFor={`template-${templateName}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {templateName === "hygiene" && "Гигиена"}
                      {templateName === "observations" && "Наблюдения"}
                      {templateName === "medications" && "Прием лекарств"}
                      {templateName === "nutrition" && "Питание"}
                      {templateName === "activities" && "Активности"}
                      {templateName === "cleaning" && "Уборка"}
                      {` (${taskTemplates[templateName as keyof typeof taskTemplates].length} задач)`}
                    </label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setActiveEditTemplate(activeEditTemplate === templateName ? null : templateName)
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => randomizeTasksTimesAndIntervals(templateName)}
                      title="Случайно распределить время и интервалы"
                    >
                      <RotateCw className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>

              {/* Task Editing Tables */}
              {Object.keys(taskTemplates).map((templateName) => (
                <div
                  key={`table-${templateName}`}
                  className={`mt-4 ${activeEditTemplate === templateName ? "block" : "hidden"}`}
                >
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium">
                      {templateName === "hygiene" && "Редактирование задач гигиены"}
                      {templateName === "observations" && "Редактирование задач наблюдения"}
                      {templateName === "medications" && "Редактирование задач приема лекарств"}
                      {templateName === "nutrition" && "Редактирование задач питания"}
                      {templateName === "activities" && "Редактирование задач активности"}
                      {templateName === "cleaning" && "Редактирование задач уборки"}
                    </h4>
                    <Button variant="outline" size="sm" onClick={() => addNewTask(templateName)}>
                      <Plus className="h-4 w-4 mr-1" /> Добавить задачу
                    </Button>
                  </div>

                  <div className="border rounded-md">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Задача</TableHead>
                          <TableHead>Интервал</TableHead>
                          <TableHead>Время</TableHead>
                          <TableHead className="w-[100px]">Действия</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {taskTemplates[templateName as keyof typeof taskTemplates].map((task, index) => (
                          <TableRow key={`${templateName}-task-${index}`}>
                            <TableCell className="font-medium">{task.text}</TableCell>
                            <TableCell>
                              {task.interval_id ? <Badge variant="outline">{task.interval_id}</Badge> : "—"}
                            </TableCell>
                            <TableCell>{task.time || "—"}</TableCell>
                            <TableCell>
                              <div className="flex space-x-1">
                                <Button variant="ghost" size="sm" onClick={() => editTask(templateName, index)}>
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm" onClick={() => deleteTask(templateName, index)}>
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              ))}

              <div className="text-sm text-muted-foreground">Выбрано шаблонов: {selectedTemplates.length}</div>
            </div>
          </TabsContent>
        </Tabs>

        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mt-4 bg-green-50 border-green-200">
            <Check className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-600">
              Задачи успешно созданы для выбранных пользователей
            </AlertDescription>
          </Alert>
        )}

        <Button
          onClick={createTasks}
          disabled={isLoading || (selectedUsers.length === 0 && !userIdsInput.trim()) || selectedTemplates.length === 0}
          className="w-full mt-4"
        >
          {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
          Создать задачи
        </Button>

        {/* Task Edit Dialog */}
        <Dialog open={editTaskDialog} onOpenChange={setEditTaskDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>{currentEditTask?.index === -1 ? "Добавить задачу" : "Редактировать задачу"}</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <label htmlFor="task-text" className="text-sm font-medium">
                  Текст задачи
                </label>
                <Input
                  id="task-text"
                  value={currentEditTask?.task.text}
                  onChange={(e) =>
                    setCurrentEditTask((prev) =>
                      prev
                        ? {
                            ...prev,
                            task: { ...prev.task, text: e.target.value },
                          }
                        : null,
                    )
                  }
                  placeholder="Введите текст задачи"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="task-interval" className="text-sm font-medium">
                  Интервал
                </label>
                <Select
                  value={currentEditTask?.task.interval_id || "none"}
                  onValueChange={(value) =>
                    setCurrentEditTask((prev) =>
                      prev
                        ? {
                            ...prev,
                            task: { ...prev.task, interval_id: value === "none" ? null : value },
                          }
                        : null,
                    )
                  }
                >
                  <SelectTrigger id="task-interval">
                    <SelectValue placeholder="Выберите интервал" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Не указан</SelectItem>
                    <SelectItem value="morning">Утро</SelectItem>
                    <SelectItem value="day">День</SelectItem>
                    <SelectItem value="evening">Вечер</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label htmlFor="task-time" className="text-sm font-medium">
                  Время
                </label>
                <Select
                  value={currentEditTask?.task.time || "none"}
                  onValueChange={(value) =>
                    setCurrentEditTask((prev) =>
                      prev
                        ? {
                            ...prev,
                            task: { ...prev.task, time: value === "none" ? null : value },
                          }
                        : null,
                    )
                  }
                >
                  <SelectTrigger id="task-time">
                    <SelectValue placeholder="Выберите время" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Не указано</SelectItem>
                    {Array.from({ length: 24 }).map((_, hour) =>
                      Array.from({ length: 2 }).map((_, half) => {
                        const time = `${hour.toString().padStart(2, "0")}:${(half * 30).toString().padStart(2, "0")}`
                        return (
                          <SelectItem key={time} value={time}>
                            {time}
                          </SelectItem>
                        )
                      }),
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditTaskDialog(false)}>
                Отмена
              </Button>
              <Button onClick={saveTaskEdit}>Сохранить</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}
