import { createServerClient } from "@/lib/supabase/server"
import { redirect } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { AdminTaskCreator } from "@/components/admin-task-creator"

export default async function AdminPage() {
  // Проверяем наличие переменных окружения
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Ошибка конфигурации</h1>
        <p>
          Отсутствуют необходимые переменные окружения для Supabase. Убедитесь, что переменные NEXT_PUBLIC_SUPABASE_URL
          и NEXT_PUBLIC_SUPABASE_ANON_KEY настроены правильно.
        </p>
      </div>
    )
  }

  try {
    const supabase = await createServerClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      redirect("/")
    }

    // Проверяем, является ли пользователь администратором
    // В реальном приложении здесь должна быть проверка роли пользователя
    const isAdmin = true // Заглушка для демонстрации

    if (!isAdmin) {
      return (
        <div className="container mx-auto max-w-3xl p-4">
          <h1 className="text-2xl font-bold mb-4">Доступ запрещен</h1>
          <p className="mb-4">У вас нет прав для доступа к этой странице.</p>
          <Link href="/">
            <Button>Вернуться на главную</Button>
          </Link>
        </div>
      )
    }

    // Получаем список пользователей
    const { data: users } = await supabase.from("users").select("id, email")

    return (
      <div className="container mx-auto max-w-3xl p-4 pb-20">
        <div className="flex items-center mb-4">
          <Link href="/">
            <Button variant="ghost" size="icon" className="mr-2">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Панель администратора</h1>
        </div>
        <AdminTaskCreator users={users || []} />
      </div>
    )
  } catch (error) {
    console.error("Ошибка на странице администратора:", error)
    return (
      <div className="container mx-auto max-w-3xl p-4">
        <h1 className="text-2xl font-bold mb-4">Ошибка загрузки</h1>
        <p className="mb-4">Произошла ошибка при загрузке панели администратора.</p>
        <Link href="/">
          <Button>Вернуться на главную</Button>
        </Link>
      </div>
    )
  }
}
