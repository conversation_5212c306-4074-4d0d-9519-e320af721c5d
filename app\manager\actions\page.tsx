import { But<PERSON> } from "@/components/ui/button"
import { createServerClient } from "@/lib/supabase/server"
import Link from "next/link"
import { Plus } from "lucide-react"

export default async function ActionsPage() {
  const supabase = await createServerClient()

  // Fetch actions with their group names
  const { data: actions, error } = await supabase
    .from("actions")
    .select(`
      *,
      task_groups (
        id,
        name
      )
    `)
    .order("created_at", { ascending: false })

  if (error) {
    console.error("Error fetching actions:", error)
  }

  async function deleteAction(id: string) {
    "use server"

    const supabase = await createServerClient()
    const { error } = await supabase.from("actions").delete().eq("id", id)

    if (error) {
      console.error("Error deleting action:", error)
      return { success: false, error: error.message }
    }

    return { success: true }
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Действия</h1>
        <Link href="/manager/actions/new">
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Добавить действие
          </Button>
        </Link>
      </div>

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Текст
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Группа
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Действия
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {actions && actions.length > 0 ? (
                actions.map((action) => (
                  <tr key={action.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{action.text}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {action.task_groups?.name || "Без группы"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Link href={`/manager/actions/${action.id}/edit`}>
                          <Button variant="outline" size="sm">
                            Изменить
                          </Button>
                        </Link>
                        <form
                          action={async () => {
                            if (confirm("Вы уверены, что хотите удалить это действие?")) {
                              const result = await deleteAction(action.id)
                              if (result.success) {
                                window.location.reload()
                              } else {
                                alert(`Ошибка при удалении: ${result.error}`)
                              }
                            }
                          }}
                        >
                          <Button variant="outline" size="sm" className="text-red-600 hover:text-red-800" type="submit">
                            Удалить
                          </Button>
                        </form>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={3} className="px-6 py-4 text-center text-sm text-gray-500">
                    {error ? "Ошибка загрузки действий" : "Действия не найдены"}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
