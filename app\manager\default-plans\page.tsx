import { But<PERSON> } from "@/components/ui/button"
import { createServerClient } from "@/lib/supabase/server"
import Link from "next/link"
import { Plus, Copy } from "lucide-react"
import { format } from "date-fns"

export default async function DefaultPlansPage() {
  const supabase = await createServerClient()

  // Fetch default plans
  const { data: defaultPlans, error } = await supabase
    .from("default_plans")
    .select(`
      *,
      items:default_plan_items(count)
    `)
    .order("created_at", { ascending: false })

  if (error) {
    console.error("Error fetching default plans:", error)
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Дефолтные планы</h1>
        <Link href="/manager/default-plans/new">
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Создать дефолтный план
          </Button>
        </Link>
      </div>

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Название
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Описание
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Кол-во действий
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Создан
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Действия
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {defaultPlans && defaultPlans.length > 0 ? (
                defaultPlans.map((plan) => (
                  <tr key={plan.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{plan.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {plan.description || "Нет описания"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {plan.items && plan.items.length > 0 ? plan.items.length : 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {format(new Date(plan.created_at), "dd.MM.yyyy")}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Link href={`/manager/default-plans/${plan.id}`}>
                          <Button variant="outline" size="sm">
                            Просмотр
                          </Button>
                        </Link>
                        <Link href={`/manager/default-plans/${plan.id}/edit`}>
                          <Button variant="outline" size="sm">
                            Изменить
                          </Button>
                        </Link>
                        <Link href={`/manager/plans/new?default_plan_id=${plan.id}`}>
                          <Button variant="outline" size="sm">
                            <Copy className="h-4 w-4 mr-1" />
                            Создать план
                          </Button>
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                    {error ? "Ошибка загрузки дефолтных планов" : "Дефолтные планы не найдены"}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
