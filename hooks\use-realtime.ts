"use client"

import { useEffect, useState, useRef } from "react"
import { createClient } from "@/lib/supabase/client"
import { useAuth } from "@/components/auth-provider"
import { useToast } from "@/hooks/use-toast"
import type { RealtimeChannel } from "@supabase/supabase-js"

export function useRealtimeSubscription() {
  const { user, refreshSession } = useAuth()
  const { toast } = useToast()
  const [channel, setChannel] = useState<RealtimeChannel | null>(null)
  const supabase = createClient()
  const channelRef = useRef<RealtimeChannel | null>(null)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!user) return

    // Функция для создания канала
    const createRealtimeChannel = () => {
      try {
        // Отписываемся от предыдущего канала, если он существует
        if (channelRef.current) {
          channelRef.current.unsubscribe()
        }

        // Создаем новый канал
        const newChannel = supabase
          .channel(`realtime-todos-${Date.now()}`) // Уникальное имя канала
          .on(
            "postgres_changes",
            {
              event: "*",
              schema: "public",
              table: "tasks",
              filter: `user_id=eq.${user.id}`,
            },
            (payload) => {
              console.log("Изменение в задачах:", payload)

              // Показываем уведомление в зависимости от типа события
              if (payload.eventType === "INSERT") {
                toast({
                  title: "Новая задача",
                  description: `Задача "${payload.new.text}" была добавлена`,
                })
              } else if (payload.eventType === "UPDATE") {
                toast({
                  title: "Задача обновлена",
                  description: `Задача "${payload.new.text}" была обновлена`,
                })
              } else if (payload.eventType === "DELETE") {
                toast({
                  title: "Задача удалена",
                  description: "Задача была удалена",
                })
              }
            },
          )
          .on(
            "postgres_changes",
            {
              event: "*",
              schema: "public",
              table: "tags",
              filter: `user_id=eq.${user.id}`,
            },
            (payload) => {
              console.log("Изменение в тегах:", payload)
            },
          )
          .on(
            "postgres_changes",
            {
              event: "*",
              schema: "public",
              table: "task_tags",
            },
            (payload) => {
              console.log("Изменение в связях задач и тегов:", payload)
            },
          )
          .on("system", { event: "disconnect" }, (payload) => {
            console.log("Отключение от Realtime:", payload)
            // Пытаемся переподключиться
            setTimeout(() => {
              createRealtimeChannel()
            }, 5000)
          })
          .subscribe((status, err) => {
            if (status === "SUBSCRIBED") {
              console.log("Подписка на Realtime успешно установлена")
              setError(null)
            } else if (status === "CHANNEL_ERROR") {
              console.error("Ошибка подписки на Realtime:", err)
              setError(err || new Error("Ошибка подписки на Realtime"))

              // Пытаемся обновить сессию и переподключиться
              refreshSession().then(() => {
                setTimeout(() => {
                  createRealtimeChannel()
                }, 5000)
              })
            }
          })

        channelRef.current = newChannel
        setChannel(newChannel)
      } catch (err) {
        console.error("Ошибка при создании канала Realtime:", err)
        setError(err instanceof Error ? err : new Error("Ошибка при создании канала Realtime"))
      }
    }

    createRealtimeChannel()

    // Отписываемся при размонтировании компонента
    return () => {
      if (channelRef.current) {
        try {
          channelRef.current.unsubscribe()
        } catch (err) {
          console.error("Ошибка при отписке от канала:", err)
        }
        channelRef.current = null
      }
    }
  }, [user, toast, supabase, refreshSession])

  // Возвращаем канал и ошибку
  return { channel, error }
}
