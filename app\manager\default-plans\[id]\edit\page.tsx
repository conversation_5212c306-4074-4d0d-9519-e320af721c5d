import { createServerClient } from "@/lib/supabase/server"
import { DefaultPlanForm } from "../../default-plan-form"
import { notFound } from "next/navigation"

export default async function EditDefaultPlanPage({ params }: { params: { id: string } }) {
  const supabase = await createServerClient()

  // Fetch default plan data
  const { data: plan, error } = await supabase.from("default_plans").select("*").eq("id", params.id).single()

  if (error || !plan) {
    notFound()
  }

  // Fetch actions for selection
  const { data: actions } = await supabase.from("actions").select("*").order("text", { ascending: true })

  // Fetch intervals for selection
  const { data: intervals } = await supabase.from("task_intervals").select("*").order("name", { ascending: true })

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Редактировать дефолтный план</h1>
      <DefaultPlanForm plan={plan} actions={actions} intervals={intervals} />
    </div>
  )
}
