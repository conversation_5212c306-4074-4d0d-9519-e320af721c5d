"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { createClient } from "@/lib/supabase/client"
import { toast } from "@/hooks/use-toast"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { PlanItemsEditor } from "./plan-items-editor"

// Define the form schema
const defaultPlanFormSchema = z.object({
  name: z.string().min(2, { message: "Название должно содержать минимум 2 символа" }),
  description: z.string().optional(),
})

type DefaultPlanFormValues = z.infer<typeof defaultPlanFormSchema>

interface DefaultPlanFormProps {
  plan?: any // The plan data for editing
  actions?: any[] // List of actions for selection
  intervals?: any[] // List of intervals for selection
}

export function DefaultPlanForm({ plan, actions = [], intervals = [] }: DefaultPlanFormProps) {
  const router = useRouter()
  const supabase = createClient()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [planItems, setPlanItems] = useState<any[]>([])

  // Initialize form with plan data or defaults
  const form = useForm<DefaultPlanFormValues>({
    resolver: zodResolver(defaultPlanFormSchema),
    defaultValues: plan
      ? {
          ...plan,
        }
      : {
          name: "",
          description: "",
        },
  })

  // Load plan items if editing an existing plan
  useEffect(() => {
    const loadPlanItems = async () => {
      if (plan?.id) {
        const { data, error } = await supabase
          .from("default_plan_items")
          .select(
            `
            *,
            action:action_id(id, text, group_id),
            interval:interval_id(id, name)
          `,
          )
          .eq("default_plan_id", plan.id)
          .order("position", { ascending: true })

        if (error) {
          console.error("Error loading plan items:", error)
          toast({
            title: "Ошибка",
            description: "Не удалось загрузить элементы плана",
            variant: "destructive",
          })
        } else {
          setPlanItems(data || [])
        }
      }
    }

    loadPlanItems()
  }, [plan, supabase])

  async function onSubmit(data: DefaultPlanFormValues) {
    setIsSubmitting(true)

    try {
      let planId = plan?.id

      if (planId) {
        // Update existing plan
        const { error } = await supabase.from("default_plans").update(data).eq("id", planId)

        if (error) throw error

        toast({
          title: "План обновлен",
          description: "Дефолтный план успешно обновлен",
        })
      } else {
        // Create new plan
        const { data: newPlan, error } = await supabase.from("default_plans").insert(data).select()

        if (error) throw error

        if (newPlan && newPlan.length > 0) {
          planId = newPlan[0].id
          toast({
            title: "План создан",
            description: "Новый дефолтный план успешно создан",
          })
        }
      }

      // Redirect to the plan detail page
      if (planId) {
        router.push(`/manager/default-plans/${planId}`)
      } else {
        router.push("/manager/default-plans")
      }
    } catch (error: any) {
      toast({
        title: "Ошибка",
        description: error.message || "Произошла ошибка при сохранении данных",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Tabs defaultValue="main" className="w-full">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="main">Основное</TabsTrigger>
            <TabsTrigger value="items" disabled={!plan?.id}>
              Элементы плана
            </TabsTrigger>
          </TabsList>

          <TabsContent value="main" className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                <div className="grid gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Название плана</FormLabel>
                        <FormControl>
                          <Input placeholder="Например: Стандартный план ухода" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Описание</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Добавьте описание плана"
                            className="min-h-[100px]"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="items" className="space-y-4">
            {plan?.id ? (
              <PlanItemsEditor
                planId={plan.id}
                planItems={planItems}
                setPlanItems={setPlanItems}
                actions={actions}
                intervals={intervals}
                isDefaultPlan={true}
              />
            ) : (
              <Card>
                <CardContent className="pt-6">
                  <p className="text-center text-muted-foreground">Сначала сохраните план, чтобы добавить элементы</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()} disabled={isSubmitting}>
            Отмена
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Сохранение..." : plan ? "Обновить план" : "Создать план"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
