import { createServerClient } from "@/lib/supabase/server"
import { format } from "date-fns"
import Link from "next/link"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

export default async function TasksPage() {
  const supabase = await createServerClient()

  // Получаем задачи за последние 30 дней
  const thirtyDaysAgo = new Date()
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
  const dateFilter = format(thirtyDaysAgo, "yyyy-MM-dd")

  const { data: tasks, error } = await supabase
    .from("tasks")
    .select(
      `
      *,
      client:client_id(id, full_name),
      interval:interval_id(id, name),
      group:group_id(id, name)
    `,
    )
    .gte("execution_date", dateFilter)
    .order("execution_date", { ascending: false })
    .limit(100)

  if (error) {
    console.error("Error fetching tasks:", error)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">Выполнено</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Ожидает</Badge>
      case "in_progress":
        return <Badge className="bg-blue-100 text-blue-800">В процессе</Badge>
      case "cancelled":
        return <Badge className="bg-red-100 text-red-800">Отменено</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Задачи</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Последние задачи</CardTitle>
        </CardHeader>
        <CardContent>
          {tasks && tasks.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Дата</TableHead>
                  <TableHead>Время</TableHead>
                  <TableHead>Клиент</TableHead>
                  <TableHead>Задача</TableHead>
                  <TableHead>Интервал</TableHead>
                  <TableHead>Группа</TableHead>
                  <TableHead>Статус</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tasks.map((task) => (
                  <TableRow key={task.id}>
                    <TableCell>
                      {task.execution_date ? format(new Date(task.execution_date), "dd.MM.yyyy") : "-"}
                    </TableCell>
                    <TableCell>{task.execution_time || "-"}</TableCell>
                    <TableCell>
                      {task.client ? (
                        <Link href={`/manager/clients/${task.client.id}`} className="text-blue-600 hover:underline">
                          {task.client.full_name}
                        </Link>
                      ) : (
                        "-"
                      )}
                    </TableCell>
                    <TableCell>
                      <Link href={`/manager/tasks/${task.id}`} className="text-blue-600 hover:underline">
                        {task.text}
                      </Link>
                    </TableCell>
                    <TableCell>{task.interval?.name || "-"}</TableCell>
                    <TableCell>{task.group?.name || "-"}</TableCell>
                    <TableCell>{getStatusBadge(task.status)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <p className="text-center py-8 text-muted-foreground">Нет задач для отображения</p>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
