"use client"

import { useEffect, useState } from "react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { checkEnvVariables } from "@/lib/env-client"
import { SupabaseClient } from "@supabase/supabase-js"

export function EnvCheck() {
  const [envStatus, setEnvStatus] = useState({ isValid: true, missingVars: [] as string[] })

  useEffect(() => {
    const status = checkEnvVariables()
    setEnvStatus(status)
  }, [])

  if (envStatus.isValid) {
    return null
  }

  return (
    <Alert variant="destructive" className="mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Ошибка конфигурации</AlertTitle>
      <AlertDescription>
        <p>Отсутствуют необходимые переменные окружения:</p>
        <ul className="list-disc pl-5 mt-2">
          {envStatus.missingVars.map((variable) => (
            <li key={variable}>{variable}</li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  )
}
