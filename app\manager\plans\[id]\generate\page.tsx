import { createServerClient } from "@/lib/supabase/server"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar } from "lucide-react"
import Link from "next/link"
import { generateTasksFromPlan } from "@/lib/services/task-generator"

export default async function GenerateTasksPage({ params }: { params: { id: string } }) {
  const supabase = await createServerClient()
  const planId = params.id

  // Загружаем данные плана
  const { data: plan, error: planError } = await supabase
    .from("plans")
    .select(`
      *,
      client:client_id (
        id,
        full_name
      )
    `)
    .eq("id", planId)
    .single()

  if (planError) {
    console.error("Error loading plan:", planError)
    return (
      <div>
        <h1 className="text-3xl font-bold mb-6">Ошибка</h1>
        <p>Не удалось загрузить план. Пожалуйста, попробуйте позже.</p>
        <Button asChild className="mt-4">
          <Link href="/manager/plans">Вернуться к списку планов</Link>
        </Button>
      </div>
    )
  }

  // Функция для генерации задач
  async function generateTasks(formData: FormData) {
    "use server"

    const startDate = formData.get("start_date") as string
    const endDate = formData.get("end_date") as string

    if (!startDate || !endDate) {
      return { success: false, error: "Необходимо указать даты начала и окончания" }
    }

    try {
      const result = await generateTasksFromPlan(planId, new Date(startDate), new Date(endDate))
      return { success: true, count: result.count }
    } catch (error: any) {
      console.error("Error generating tasks:", error)
      return { success: false, error: error.message || "Произошла ошибка при генерации задач" }
    }
  }

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Генерация задач</h1>
      <Card>
        <CardHeader>
          <CardTitle>План: {plan.name}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <p className="text-sm text-muted-foreground">
              {plan.client ? `Клиент: ${plan.client.full_name}` : "План без клиента"}
            </p>
            <p className="text-sm text-muted-foreground mt-1">Статус: {plan.is_active ? "Активный" : "Неактивный"}</p>
          </div>

          <form action={generateTasks}>
            <div className="grid gap-4 mb-6">
              <div>
                <label htmlFor="start_date" className="block text-sm font-medium mb-1">
                  Дата начала
                </label>
                <input
                  type="date"
                  id="start_date"
                  name="start_date"
                  className="w-full p-2 border rounded-md"
                  required
                />
              </div>
              <div>
                <label htmlFor="end_date" className="block text-sm font-medium mb-1">
                  Дата окончания
                </label>
                <input type="date" id="end_date" name="end_date" className="w-full p-2 border rounded-md" required />
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" asChild>
                <Link href={`/manager/plans/${planId}`}>Отмена</Link>
              </Button>
              <Button type="submit">
                <Calendar className="mr-2 h-4 w-4" /> Сгенерировать задачи
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
