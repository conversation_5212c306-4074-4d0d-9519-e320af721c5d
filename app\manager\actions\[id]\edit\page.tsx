import { createServerClient } from "@/lib/supabase/server"
import { ActionForm } from "../../action-form"
import { notFound } from "next/navigation"

export default async function EditActionPage({ params }: { params: { id: string } }) {
  const supabase = await createServerClient()

  // Fetch action data
  const { data: action, error } = await supabase.from("actions").select("*").eq("id", params.id).single()

  if (error || !action) {
    notFound()
  }

  // Fetch task groups for selection
  const { data: groups } = await supabase.from("task_groups").select("*").order("name", { ascending: true })

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Редактировать действие</h1>
      <ActionForm action={action} groups={groups} />
    </div>
  )
}
