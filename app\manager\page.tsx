import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { createServerClient } from "@/lib/supabase/server"
import { Users, ListTodo, ClipboardList, ClipboardCheck, Calendar } from "lucide-react"
import Link from "next/link"

export default async function ManagerDashboard() {
  const supabase = await createServerClient()

  // Fetch counts for dashboard
  const [
    { count: clientsCount },
    { count: actionsCount },
    { count: defaultPlansCount },
    { count: plansCount },
    { count: tasksCount },
  ] = await Promise.all([
    supabase.from("clients").select("*", { count: "exact", head: true }),
    supabase.from("actions").select("*", { count: "exact", head: true }),
    supabase.from("default_plans").select("*", { count: "exact", head: true }),
    supabase.from("plans").select("*", { count: "exact", head: true }),
    supabase.from("tasks_2").select("*", { count: "exact", head: true }),
  ])

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8">Панель управления</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Link href="/manager/clients">
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Клиенты</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{clientsCount || 0}</div>
              <p className="text-xs text-muted-foreground">Всего клиентов в системе</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/manager/actions">
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Действия</CardTitle>
              <ListTodo className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{actionsCount || 0}</div>
              <p className="text-xs text-muted-foreground">Шаблоны задач</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/manager/default-plans">
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Дефолтные планы</CardTitle>
              <ClipboardList className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{defaultPlansCount || 0}</div>
              <p className="text-xs text-muted-foreground">Шаблоны планов</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/manager/plans">
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Планы клиентов</CardTitle>
              <ClipboardCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{plansCount || 0}</div>
              <p className="text-xs text-muted-foreground">Активных планов: {plansCount ? "?" : 0}</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/manager/tasks">
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Задачи</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{tasksCount || 0}</div>
              <p className="text-xs text-muted-foreground">Сгенерированные задачи</p>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  )
}
