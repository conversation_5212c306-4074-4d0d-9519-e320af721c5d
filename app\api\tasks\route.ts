import { NextResponse } from "next/server"
import { mockTasks } from "@/app/mock-data"

export async function GET() {
  try {
    const response = await fetch("https://6d21d1646ba0.vps.myjino.ru/api/sources", {
      cache: "no-store",
      next: { revalidate: 0 },
    })

    if (!response.ok) {
      throw new Error(`API responded with status: ${response.status}`)
    }

    let data
    try {
      data = await response.json()
    } catch (parseError) {
      console.error("Error parsing API response:", parseError)
      throw new Error("Invalid JSON response from API")
    }

    // Log the structure of the data to help debug
    console.log("API response structure:", typeof data)

    // Check if data has the member property with an array
    if (data && typeof data === "object" && Array.isArray(data.member)) {
      console.log("Found member array with length:", data.member.length)
      return NextResponse.json(data.member)
    } else {
      console.error("API response does not contain expected member array:", data)
      throw new Error("API response does not contain expected member array")
    }
  } catch (error) {
    console.error("Error fetching from external API:", error)
    // Return mock data as a fallback
    console.log("Using mock data as fallback")
    return NextResponse.json(mockTasks)
  }
}
