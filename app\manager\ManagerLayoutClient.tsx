"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { LogOut, Menu, X } from "lucide-react"

export default function ManagerLayoutClient({
  children,
}: {
  children: React.ReactNode
}) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const pathname = usePathname()
  const supabase = createClient()

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    window.location.href = "/"
  }

  const menuItems = [
    { href: "/manager", label: "Главная" },
    { href: "/manager/clients", label: "Клиенты" },
    { href: "/manager/actions", label: "Действия" },
    { href: "/manager/default-plans", label: "Шаблоны планов" },
    { href: "/manager/plans", label: "Планы" },
    { href: "/manager/tasks", label: "Задачи" },
    { href: "/manager/reports", label: "Отчеты" },
  ]

  return (
    <div className="flex min-h-screen">
      {/* Sidebar for desktop */}
      <div className="hidden md:flex flex-col w-64 bg-gray-100 p-4">
        <div className="text-xl font-bold mb-6">Панель менеджера</div>
        <nav className="space-y-2 flex-1">
          {menuItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={`block px-4 py-2 rounded-md ${
                pathname === item.href ? "bg-gray-200 font-medium" : "hover:bg-gray-200"
              }`}
            >
              {item.label}
            </Link>
          ))}
        </nav>
        <Button variant="outline" className="mt-auto w-full" onClick={handleSignOut}>
          <LogOut className="mr-2 h-4 w-4" />
          Выйти
        </Button>
      </div>

      {/* Mobile menu button */}
      <div className="md:hidden fixed top-0 left-0 right-0 z-30 bg-white border-b p-4 flex justify-between items-center">
        <div className="text-lg font-bold">Панель менеджера</div>
        <Button variant="ghost" size="icon" onClick={() => setIsMenuOpen(!isMenuOpen)}>
          {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </Button>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden fixed inset-0 z-20 bg-white pt-16">
          <nav className="p-4 space-y-2">
            {menuItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`block px-4 py-2 rounded-md ${
                  pathname === item.href ? "bg-gray-100 font-medium" : "hover:bg-gray-100"
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                {item.label}
              </Link>
            ))}
            <Button variant="outline" className="w-full mt-4" onClick={handleSignOut}>
              <LogOut className="mr-2 h-4 w-4" />
              Выйти
            </Button>
          </nav>
        </div>
      )}

      {/* Main content */}
      <div className="flex-1 md:ml-64 pt-16 md:pt-0">
        <div className="p-6">{children}</div>
      </div>
    </div>
  )
}
