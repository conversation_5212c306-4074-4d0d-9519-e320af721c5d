"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState, useRef } from "react"
import type { Session, User } from "@supabase/supabase-js"
import { createClient, resetClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"

type AuthContextType = {
  user: User | null
  session: Session | null
  isLoading: boolean
  signOut: () => Promise<void>
  refreshSession: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  isLoading: true,
  signOut: async () => {},
  refreshSession: async () => {},
})

export const useAuth = () => useContext(AuthContext)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const { toast } = useToast()
  const supabase = createClient()
  const authListenerRef = useRef<{ unsubscribe: () => void } | null>(null)

  // Функция для обновления сессии
  const refreshSession = async () => {
    try {
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession()

      if (error) {
        throw error
      }

      if (session) {
        setSession(session)
        setUser(session.user)
      } else {
        setSession(null)
        setUser(null)
      }
    } catch (error) {
      console.error("Error refreshing session:", error)
      toast({
        title: "Ошибка сессии",
        description: "Произошла ошибка при обновлении сессии. Пожалуйста, войдите снова.",
        variant: "destructive",
      })
      // Сбрасываем клиент и перенаправляем на главную
      resetClient()
      router.push("/")
    }
  }

  // Инициализация сессии
  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true)
      try {
        await refreshSession()
      } catch (error) {
        console.error("Error initializing auth:", error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeAuth()

    // Настраиваем слушатель изменений аутентификации
    try {
      const {
        data: { subscription },
      } = supabase.auth.onAuthStateChange((_event, session) => {
        setSession(session)
        setUser(session?.user || null)
        router.refresh()
      })

      // Сохраняем саму подписку, а не весь объект
      authListenerRef.current = subscription
    } catch (error) {
      console.error("Error setting up auth listener:", error)
    }

    // Очистка при размонтировании
    return () => {
      if (authListenerRef.current) {
        authListenerRef.current.unsubscribe()
      }
    }
  }, [router, supabase, toast])

  // Периодическое обновление сессии для предотвращения истечения срока действия
  useEffect(() => {
    const intervalId = setInterval(
      () => {
        if (session) {
          refreshSession()
        }
      },
      10 * 60 * 1000,
    ) // Каждые 10 минут

    return () => clearInterval(intervalId)
  }, [session])

  const signOut = async () => {
    try {
      await supabase.auth.signOut()
      setSession(null)
      setUser(null)

      // Сбрасываем клиент Supabase
      resetClient()

      // Перенаправляем на главную страницу
      window.location.href = "/"
    } catch (error) {
      console.error("Error signing out:", error)
      toast({
        title: "Ошибка выхода",
        description: "Произошла ошибка при выходе из системы.",
        variant: "destructive",
      })
    }
  }

  const value = {
    user,
    session,
    isLoading,
    signOut,
    refreshSession,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
