import { createServerClient } from "@/lib/supabase/server"
import { format } from "date-fns"
import { ru } from "date-fns/locale"
import Link from "next/link"
import { notFound } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default async function TaskDetailPage({ params }: { params: { id: string } }) {
  const supabase = await createServerClient()

  // Получаем задачу
  const { data: task, error } = await supabase
    .from("tasks")
    .select(
      `
      *,
      client:client_id(id, full_name),
      interval:interval_id(id, name),
      group:group_id(id, name)
    `,
    )
    .eq("id", params.id)
    .single()

  if (error || !task) {
    notFound()
  }

  // Получаем историю задачи
  const { data: history } = await supabase
    .from("task_history")
    .select(
      `
      *,
      user:user_id(id, email)
    `,
    )
    .eq("task_id", params.id)
    .order("created_at", { ascending: false })

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "Ожидает"
      case "in_progress":
        return "В процессе"
      case "completed":
        return "Выполнено"
      case "cancelled":
        return "Отменено"
      default:
        return status
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">Выполнено</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Ожидает</Badge>
      case "in_progress":
        return <Badge className="bg-blue-100 text-blue-800">В процессе</Badge>
      case "cancelled":
        return <Badge className="bg-red-100 text-red-800">Отменено</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getActionText = (action: string) => {
    switch (action) {
      case "create":
        return "Создание"
      case "update":
        return "Обновление"
      case "delete":
        return "Удаление"
      case "add_tag":
        return "Добавление тега"
      case "remove_tag":
        return "Удаление тега"
      default:
        return action
    }
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Задача: {task.text}</h1>
        <div className="flex space-x-2">{getStatusBadge(task.status)}</div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Информация о задаче</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-2">
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Текст задачи:</dt>
                <dd className="col-span-2">{task.text}</dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Клиент:</dt>
                <dd className="col-span-2">
                  {task.client ? (
                    <Link href={`/manager/clients/${task.client.id}`} className="text-blue-600 hover:underline">
                      {task.client.full_name}
                    </Link>
                  ) : (
                    "Не указан"
                  )}
                </dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Дата выполнения:</dt>
                <dd className="col-span-2">
                  {task.execution_date ? format(new Date(task.execution_date), "dd MMMM yyyy", { locale: ru }) : "-"}
                </dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Время выполнения:</dt>
                <dd className="col-span-2">{task.execution_time || "-"}</dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Интервал:</dt>
                <dd className="col-span-2">{task.interval?.name || "-"}</dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Группа:</dt>
                <dd className="col-span-2">{task.group?.name || "-"}</dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Статус:</dt>
                <dd className="col-span-2">{getStatusText(task.status)}</dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Комментарий:</dt>
                <dd className="col-span-2">{task.comment || "-"}</dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Создана:</dt>
                <dd className="col-span-2">
                  {task.created_at ? format(new Date(task.created_at), "dd.MM.yyyy HH:mm") : "-"}
                </dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Обновлена:</dt>
                <dd className="col-span-2">
                  {task.updated_at ? format(new Date(task.updated_at), "dd.MM.yyyy HH:mm") : "-"}
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>История задачи</CardTitle>
          </CardHeader>
          <CardContent>
            {history && history.length > 0 ? (
              <div className="space-y-4">
                {history.map((entry) => (
                  <div key={entry.id} className="border-b pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <span className="font-medium">{getActionText(entry.action)}</span>
                        <span className="text-sm text-gray-500 ml-2">
                          {entry.created_at ? format(new Date(entry.created_at), "dd.MM.yyyy HH:mm") : "-"}
                        </span>
                      </div>
                      <div className="text-sm text-gray-500">{entry.user?.email || "Система"}</div>
                    </div>
                    {entry.details && (
                      <div className="mt-2 text-sm">
                        <pre className="whitespace-pre-wrap bg-gray-50 p-2 rounded">
                          {JSON.stringify(entry.details, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center text-muted-foreground">Нет записей в истории</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
