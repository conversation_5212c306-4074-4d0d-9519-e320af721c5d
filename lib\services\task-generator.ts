import { createServerClient } from "@/lib/supabase/server"
import { format, getDay, getDate, getMonth } from "date-fns"

export async function generateTasksFromPlan(planId: string, startDate: Date, endDate: Date) {
  const supabase = await createServerClient()

  // Загружаем план
  const { data: plan, error: planError } = await supabase.from("plans").select("*").eq("id", planId).single()

  if (planError) {
    throw new Error(`Ошибка загрузки плана: ${planError.message}`)
  }

  // Загружаем элементы плана
  const { data: planItems, error: itemsError } = await supabase
    .from("plan_items")
    .select(`
      *,
      action:action_id (*)
    `)
    .eq("plan_id", planId)
    .order("position", { ascending: true })

  if (itemsError) {
    throw new Error(`Ошибка загрузки элементов плана: ${itemsError.message}`)
  }

  if (!planItems || planItems.length === 0) {
    throw new Error("План не содержит элементов")
  }

  // Генерируем задачи
  const tasks = []

  for (const item of planItems) {
    const tasksForItem = await generateTasksForPlanItem(item, plan, startDate, endDate)
    tasks.push(...tasksForItem)
  }

  // Сохраняем задачи в базу данных
  if (tasks.length > 0) {
    const { error: insertError } = await supabase.from("tasks_2").insert(tasks)

    if (insertError) {
      throw new Error(`Ошибка сохранения задач: ${insertError.message}`)
    }
  }

  return { success: true, count: tasks.length }
}

async function generateTasksForPlanItem(planItem: any, plan: any, startDate: Date, endDate: Date) {
  const tasks = []
  const currentDate = new Date(startDate)
  const recurrenceType = planItem.recurrence_type || "once"
  const recurrenceValue = planItem.recurrence_value || {}

  // Для однократных задач
  if (recurrenceType === "once") {
    tasks.push(createTask(planItem, plan, startDate))
    return tasks
  }

  // Для повторяющихся задач
  while (currentDate <= endDate) {
    if (shouldCreateTaskOnDate(currentDate, recurrenceType, recurrenceValue)) {
      tasks.push(createTask(planItem, plan, new Date(currentDate)))
    }

    // Переходим к следующему дню
    currentDate.setDate(currentDate.getDate() + 1)
  }

  return tasks
}

function shouldCreateTaskOnDate(date: Date, recurrenceType: string, recurrenceValue: any) {
  switch (recurrenceType) {
    case "daily":
      return true

    case "weekly":
      // Проверяем, входит ли день недели в список дней
      const dayOfWeek = getDay(date) || 7 // 0-6 (воскресенье-суббота), преобразуем 0 в 7 для воскресенья
      return recurrenceValue.days?.includes(dayOfWeek) || false

    case "monthly":
      // Проверяем, входит ли день месяца в список дней
      const dayOfMonth = getDate(date)
      return recurrenceValue.days?.includes(dayOfMonth) || false

    case "yearly":
      // Проверяем, входит ли дата (месяц-день) в список дат
      const monthDay = `${getMonth(date) + 1}-${getDate(date)}`
      return recurrenceValue.dates?.includes(monthDay) || false

    default:
      return false
  }
}

function createTask(planItem: any, plan: any, date: Date) {
  return {
    user_id: null, // Будет заполнено при назначении задачи пользователю
    text: planItem.action.text,
    completed: false,
    status: "pending",
    comment: "",
    execution_date: format(date, "yyyy-MM-dd"),
    execution_time: planItem.time,
    interval_id: planItem.interval_id,
    group_id: planItem.action.group_id,
    created_by: null, // Задача создана системой
    plan_id: plan.id,
    plan_item_id: planItem.id,
    client_id: plan.client_id,
  }
}

// Function to delete all tasks for a plan
export async function deleteTasksForPlan(planId: string) {
  const supabase = await createServerClient()

  try {
    const { error } = await supabase.from("tasks_2").delete().eq("plan_id", planId)

    if (error) {
      throw new Error(error.message)
    }

    return {
      success: true,
    }
  } catch (error: any) {
    console.error("Error deleting tasks:", error)
    return {
      success: false,
      error: error.message,
    }
  }
}

// Helper function to get task dates based on recurrence settings
// function getTaskDates(planItem: any, startDate: Date, endDate: Date) {
//   const dates: Date[] = []

//   // For one-time tasks
//   if (planItem.recurrence_type === "once") {
//     const taskDate = new Date(planItem.start_date)
//     if (!isAfter(taskDate, endDate) && !isBefore(taskDate, startDate)) {
//       dates.push(taskDate)
//     }
//     return dates
//   }

//   // For recurring tasks
//   let currentDate = new Date(startDate)
//   const itemStartDate = planItem.start_date ? new Date(planItem.start_date) : null
//   const itemEndDate = planItem.end_date ? new Date(planItem.end_date) : null

//   // If item has a start date and it's after our range start, use that instead
//   if (itemStartDate && isAfter(itemStartDate, currentDate)) {
//     currentDate = new Date(itemStartDate)
//   }

//   // Process until we reach the end date
//   while (!isAfter(currentDate, endDate)) {
//     // Check if we've passed the item's end date (if it has one)
//     if (itemEndDate && isAfter(currentDate, itemEndDate)) {
//       break
//     }

//     // Check recurrence rules
//     let shouldAddDate = false

//     switch (planItem.recurrence_type) {
//       case "daily":
//         shouldAddDate = true
//         break

//       case "weekly":
//         // Check if current day is in the selected days
//         if (planItem.recurrence_value?.days) {
//           const dayOfWeek = getDay(currentDate) // 0 = Sunday, 1 = Monday, etc.
//           shouldAddDate = planItem.recurrence_value.days.includes(dayOfWeek)
//         }
//         break

//       case "monthly":
//         // Check if current day of month is selected
//         if (planItem.recurrence_value?.days) {
//           const dayOfMonth = currentDate.getDate()
//           shouldAddDate = planItem.recurrence_value.days.includes(dayOfMonth)
//         }
//         break

//       case "yearly":
//         // Check if current month and day match
//         if (planItem.recurrence_value?.dates) {
//           const currentMonthDay = `${currentDate.getMonth() + 1}-${currentDate.getDate()}`
//           shouldAddDate = planItem.recurrence_value.dates.includes(currentMonthDay)
//         }
//         break
//     }

//     if (shouldAddDate) {
//       dates.push(new Date(currentDate))
//     }

//     // Move to next day
//     currentDate = addDays(currentDate, 1)
//   }

//   return dates
// }
