"use client"

import { useRef, useState, useEffect } from "react"
import { createClient } from "@/lib/supabase/client"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ReportFilters } from "../report-filters"
import { PrintReportButton } from "../print-report-button"
import { format } from "date-fns"
import { Loader2 } from "lucide-react"

export default function DetailedReportPage() {
  const [loading, setLoading] = useState(true)
  const [tasks, setTasks] = useState<any[]>([])
  const [clients, setClients] = useState<any[]>([])
  const [intervals, setIntervals] = useState<any[]>([])
  const [groups, setGroups] = useState<any[]>([])
  const [filters, setFilters] = useState<any>({})
  const reportRef = useRef<HTMLDivElement>(null)
  const supabase = createClient()

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)

      // Загружаем клиентов
      const { data: clientsData } = await supabase.from("clients").select("id, full_name").order("full_name")
      if (clientsData) setClients(clientsData)

      // Загружаем интервалы
      const { data: intervalsData } = await supabase.from("task_intervals").select("id, name").order("name")
      if (intervalsData) setIntervals(intervalsData)

      // Загружаем группы
      const { data: groupsData } = await supabase.from("task_groups").select("id, name").order("name")
      if (groupsData) setGroups(groupsData)

      // Загружаем задачи с учетом фильтров
      await fetchTasks(filters)
    }

    fetchData()
  }, [])

  const fetchTasks = async (filters: any) => {
    setLoading(true)

    let query = supabase
      .from("tasks")
      .select(
        `
        *,
        client:client_id(id, full_name),
        interval:interval_id(id, name),
        group:group_id(id, name)
      `,
      )
      .order("execution_date", { ascending: false })

    // Применяем фильтры
    if (filters.client_id) {
      query = query.eq("client_id", filters.client_id)
    }

    if (filters.interval_id) {
      query = query.eq("interval_id", filters.interval_id)
    }

    if (filters.group_id) {
      query = query.eq("group_id", filters.group_id)
    }

    if (filters.status) {
      query = query.eq("status", filters.status)
    }

    if (filters.start_date) {
      const startDate = format(filters.start_date, "yyyy-MM-dd")
      query = query.gte("execution_date", startDate)
    }

    if (filters.end_date) {
      const endDate = format(filters.end_date, "yyyy-MM-dd")
      query = query.lte("execution_date", endDate)
    }

    const { data, error } = await query

    if (error) {
      console.error("Error fetching tasks:", error)
    } else {
      setTasks(data || [])
    }

    setLoading(false)
  }

  const handleApplyFilters = (newFilters: any) => {
    setFilters(newFilters)
    fetchTasks(newFilters)
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "Ожидает"
      case "in_progress":
        return "В процессе"
      case "completed":
        return "Выполнено"
      case "cancelled":
        return "Отменено"
      default:
        return status
    }
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Детальный отчет по задачам</h1>
        <PrintReportButton reportRef={reportRef} title="Детальный отчет по задачам" />
      </div>

      <ReportFilters
        clients={clients}
        intervals={intervals}
        groups={groups}
        onApplyFilters={handleApplyFilters}
        showCommentFilter={true}
      />

      <div ref={reportRef}>
        <Card>
          <CardHeader className="print-only" style={{ display: "none" }}>
            <CardTitle>Детальный отчет по задачам</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : tasks.length === 0 ? (
              <p className="text-center py-8 text-muted-foreground">Нет данных для отображения</p>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Дата</TableHead>
                    <TableHead>Время</TableHead>
                    <TableHead>Клиент</TableHead>
                    <TableHead>Задача</TableHead>
                    <TableHead>Интервал</TableHead>
                    <TableHead>Группа</TableHead>
                    <TableHead>Статус</TableHead>
                    <TableHead>Комментарий</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tasks.map((task) => (
                    <TableRow key={task.id}>
                      <TableCell>
                        {task.execution_date ? format(new Date(task.execution_date), "dd.MM.yyyy") : "-"}
                      </TableCell>
                      <TableCell>{task.execution_time || "-"}</TableCell>
                      <TableCell>{task.client?.full_name || "-"}</TableCell>
                      <TableCell>{task.text}</TableCell>
                      <TableCell>{task.interval?.name || "-"}</TableCell>
                      <TableCell>{task.group?.name || "-"}</TableCell>
                      <TableCell>{getStatusText(task.status)}</TableCell>
                      <TableCell>{task.comment || "-"}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
