"use server"

import { createClient as createActionClient } from "@/lib/supabase/client"
import { revalidatePath } from "next/cache"
import { format } from "date-fns"

// Типы
export interface Task {
  id: string
  text: string
  completed: boolean
  status: "pending" | "completed" | "cancelled"
  comment: string
  tags: string[]
  created_at: string
  updated_at: string
  execution_date: string | null
  execution_time: string | null
  interval_id: string | null
  group_id: string | null
  created_by: string | null
  interval?: TaskInterval
  group?: TaskGroup
}

export interface TaskInterval {
  id: string
  name: string
  start_time: string
  end_time: string
}

export interface TaskGroup {
  id: string
  name: string
  icon: string | null
}

export interface Tag {
  id: string
  name: string
}

// Получение задач пользователя
export async function getTasks(date?: string) {
  const supabase = createActionClient()

  // Проверяем аутентификацию
  const {
    data: { session },
  } = await supabase.auth.getSession()
  if (!session) {
    return { tasks: [], tags: [], intervals: [], groups: [] }
  }

  // Если дата не указана, используем текущую
  const filterDate = date || format(new Date(), "yyyy-MM-dd")

  // Получаем задачи
  const { data: tasks, error: tasksError } = await supabase
    .from("tasks")
    .select("*, interval:interval_id(*), group:group_id(*)")
    .eq("execution_date", filterDate)
    .order("execution_time", { ascending: true })

  if (tasksError) {
    console.error("Error fetching tasks:", tasksError)
    return { tasks: [], tags: [], intervals: [], groups: [] }
  }

  // Получаем теги
  const { data: tags, error: tagsError } = await supabase.from("tags").select("*")

  if (tagsError) {
    console.error("Error fetching tags:", tagsError)
    return { tasks: tasks || [], tags: [], intervals: [], groups: [] }
  }

  // Получаем связи задач и тегов
  const { data: taskTags, error: taskTagsError } = await supabase.from("task_tags").select("task_id, tag_id")

  if (taskTagsError) {
    console.error("Error fetching task tags:", taskTagsError)
    return { tasks: tasks || [], tags: tags || [], intervals: [], groups: [] }
  }

  // Получаем интервалы
  const { data: intervals, error: intervalsError } = await supabase.from("task_intervals").select("*")

  if (intervalsError) {
    console.error("Error fetching intervals:", intervalsError)
    return { tasks: tasks || [], tags: tags || [], intervals: [], groups: [] }
  }

  // Получаем группы
  const { data: groups, error: groupsError } = await supabase.from("task_groups").select("*")

  if (groupsError) {
    console.error("Error fetching groups:", groupsError)
    return { tasks: tasks || [], tags: tags || [], intervals: intervals || [], groups: [] }
  }

  // Формируем задачи с тегами
  const tasksWithTags =
    tasks?.map((task) => {
      const taskTagIds = taskTags?.filter((tt) => tt.task_id === task.id).map((tt) => tt.tag_id) || []

      const taskTagNames = tags?.filter((tag) => taskTagIds.includes(tag.id)).map((tag) => tag.name) || []

      return {
        ...task,
        tags: taskTagNames,
      }
    }) || []

  return {
    tasks: tasksWithTags,
    tags: tags?.map((tag) => ({ id: tag.id, name: tag.name })) || [],
    intervals: intervals || [],
    groups: groups || [],
  }
}

// Создание новой задачи
export async function createTask(
  text: string,
  executionDate: string,
  executionTime?: string,
  intervalId?: string,
  groupId?: string,
) {
  const supabase = createActionClient()

  // Проверяем аутентификацию
  const {
    data: { session },
  } = await supabase.auth.getSession()
  if (!session) {
    return { error: "Не авторизован" }
  }

  // Создаем задачу
  const { data: task, error } = await supabase
    .from("tasks")
    .insert({
      text,
      user_id: session.user.id,
      created_by: session.user.id,
      execution_date: executionDate,
      execution_time: executionTime,
      interval_id: intervalId,
      group_id: groupId,
    })
    .select()
    .single()

  if (error) {
    console.error("Error creating task:", error)
    return { error: error.message }
  }

  // Записываем действие в историю
  await supabase.from("task_history").insert({
    task_id: task.id,
    user_id: session.user.id,
    action: "create",
    details: { text, execution_date: executionDate },
  })

  revalidatePath("/")
  return { task }
}

// Обновление задачи
export async function updateTask(
  id: string,
  updates: {
    text?: string
    completed?: boolean
    status?: "pending" | "completed" | "cancelled"
    comment?: string
    execution_date?: string
    execution_time?: string
    interval_id?: string
    group_id?: string
  },
) {
  const supabase = createActionClient()

  // Проверяем аутентификацию
  const {
    data: { session },
  } = await supabase.auth.getSession()
  if (!session) {
    return { error: "Не авторизован" }
  }

  // Обновляем задачу
  const { data: task, error } = await supabase.from("tasks").update(updates).eq("id", id).select().single()

  if (error) {
    console.error("Error updating task:", error)
    return { error: error.message }
  }

  // Записываем действие в историю
  await supabase.from("task_history").insert({
    task_id: id,
    user_id: session.user.id,
    action: "update",
    details: updates,
  })

  revalidatePath("/")
  return { task }
}

// Удаление задачи
export async function deleteTask(id: string) {
  const supabase = createActionClient()

  // Проверяем аутентификацию
  const {
    data: { session },
  } = await supabase.auth.getSession()
  if (!session) {
    return { error: "Не авторизован" }
  }

  // Получаем задачу перед удалением для проверки и истории
  const { data: task } = await supabase.from("tasks").select("*").eq("id", id).single()

  // Проверяем, что пользователь является создателем задачи
  if (task && task.created_by && task.created_by !== session.user.id) {
    return { error: "Вы можете удалять только созданные вами задачи" }
  }

  // Удаляем задачу
  const { error } = await supabase.from("tasks").delete().eq("id", id)

  if (error) {
    console.error("Error deleting task:", error)
    return { error: error.message }
  }

  // Записываем действие в историю
  if (task) {
    await supabase.from("task_history").insert({
      task_id: id,
      user_id: session.user.id,
      action: "delete",
      details: task,
    })
  }

  revalidatePath("/")
  return { success: true }
}

// Получение интервалов
export async function getIntervals() {
  const supabase = createActionClient()

  const { data: intervals, error } = await supabase.from("task_intervals").select("*")

  if (error) {
    console.error("Error fetching intervals:", error)
    return { intervals: [] }
  }

  return { intervals: intervals || [] }
}

// Получение групп
export async function getGroups() {
  const supabase = createActionClient()

  const { data: groups, error } = await supabase.from("task_groups").select("*")

  if (error) {
    console.error("Error fetching groups:", error)
    return { groups: [] }
  }

  return { groups: groups || [] }
}

// Создание тега
export async function createTag(name: string) {
  const supabase = createActionClient()

  // Проверяем аутентификацию
  const {
    data: { session },
  } = await supabase.auth.getSession()
  if (!session) {
    return { error: "Не авторизован" }
  }

  // Проверяем, существует ли уже такой тег
  const { data: existingTag } = await supabase
    .from("tags")
    .select("*")
    .eq("name", name)
    .eq("user_id", session.user.id)
    .maybeSingle()

  if (existingTag) {
    return { tag: existingTag }
  }

  // Создаем тег
  const { data: tag, error } = await supabase
    .from("tags")
    .insert({
      name,
      user_id: session.user.id,
    })
    .select()
    .single()

  if (error) {
    console.error("Error creating tag:", error)
    return { error: error.message }
  }

  revalidatePath("/")
  return { tag }
}

// Добавление тега к задаче
export async function addTagToTask(taskId: string, tagName: string) {
  const supabase = createActionClient()

  // Проверяем аутентификацию
  const {
    data: { session },
  } = await supabase.auth.getSession()
  if (!session) {
    return { error: "Не авторизован" }
  }

  // Получаем или создаем тег
  const { tag, error: tagError } = await createTag(tagName)
  if (tagError) {
    return { error: tagError }
  }

  if (!tag) {
    return { error: "Не удалось создать тег" }
  }

  // Проверяем, существует ли уже такая связь
  const { data: existingTaskTag } = await supabase
    .from("task_tags")
    .select("*")
    .eq("task_id", taskId)
    .eq("tag_id", tag.id)
    .maybeSingle()

  if (existingTaskTag) {
    return { success: true }
  }

  // Добавляем связь
  const { error } = await supabase.from("task_tags").insert({
    task_id: taskId,
    tag_id: tag.id,
  })

  if (error) {
    console.error("Error adding tag to task:", error)
    return { error: error.message }
  }

  // Записываем действие в историю
  await supabase.from("task_history").insert({
    task_id: taskId,
    user_id: session.user.id,
    action: "add_tag",
    details: { tag_name: tagName },
  })

  revalidatePath("/")
  return { success: true }
}

// Удаление тега из задачи
export async function removeTagFromTask(taskId: string, tagName: string) {
  const supabase = createActionClient()

  // Проверяем аутентификацию
  const {
    data: { session },
  } = await supabase.auth.getSession()
  if (!session) {
    return { error: "Не авторизован" }
  }

  // Находим ID тега по имени
  const { data: tag } = await supabase
    .from("tags")
    .select("*")
    .eq("name", tagName)
    .eq("user_id", session.user.id)
    .maybeSingle()

  if (!tag) {
    return { error: "Тег не найден" }
  }

  // Удаляем связь
  const { error } = await supabase.from("task_tags").delete().eq("task_id", taskId).eq("tag_id", tag.id)

  if (error) {
    console.error("Error removing tag from task:", error)
    return { error: error.message }
  }

  // Записываем действие в историю
  await supabase.from("task_history").insert({
    task_id: taskId,
    user_id: session.user.id,
    action: "remove_tag",
    details: { tag_name: tagName },
  })

  revalidatePath("/")
  return { success: true }
}
