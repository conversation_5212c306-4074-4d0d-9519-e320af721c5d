"use client"

import React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { AlertCircle, RefreshCw } from "lucide-react"
import { resetClient } from "@/lib/supabase/client"

interface ErrorBoundaryProps {
  children: React.ReactNode
}

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Error caught by ErrorBoundary:", error, errorInfo)
  }

  handleReset = () => {
    // Сбрасываем клиент Supabase
    resetClient()
    // Сбрасываем состояние ошибки
    this.setState({ hasError: false, error: null })
    // Перезагружаем страницу
    window.location.reload()
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
          <h2 className="text-2xl font-bold mb-2">Что-то пошло не так</h2>
          <p className="text-muted-foreground mb-6">
            Произошла ошибка при загрузке приложения. Пожалуйста, попробуйте перезагрузить страницу.
          </p>
          <div className="space-y-2">
            <Button onClick={this.handleReset} className="w-full">
              <RefreshCw className="mr-2 h-4 w-4" />
              Перезагрузить приложение
            </Button>
          </div>
          {this.state.error && (
            <div className="mt-4 p-4 bg-red-50 rounded-md text-left">
              <p className="text-sm text-red-800 font-mono">{this.state.error.toString()}</p>
            </div>
          )}
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
