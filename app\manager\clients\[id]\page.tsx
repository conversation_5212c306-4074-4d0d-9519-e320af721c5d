import { Button } from "@/components/ui/button"
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card"
import { createServerClient } from "@/lib/supabase/server"
import Link from "next/link"
import { notFound } from "next/navigation"
import { format } from "date-fns"
import { ru } from "date-fns/locale"
import { Pencil, Plus } from "lucide-react"

export default async function ClientDetailPage({ params }: { params: { id: string } }) {
  const supabase = await createServerClient()

  // Fetch client data
  const { data: client, error } = await supabase.from("clients").select("*").eq("id", params.id).single()

  if (error || !client) {
    notFound()
  }

  // Fetch client relatives
  const { data: relatives } = await supabase
    .from("client_relatives")
    .select("*")
    .eq("client_id", params.id)
    .order("created_at", { ascending: true })

  // Fetch active plan
  const { data: activePlan } = await supabase
    .from("plans")
    .select("*")
    .eq("client_id", params.id)
    .eq("is_active", true)
    .single()

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">{client.full_name}</h1>
        <div className="flex space-x-2">
          <Link href={`/manager/clients/${params.id}/edit`}>
            <Button variant="outline">
              <Pencil className="mr-2 h-4 w-4" /> Редактировать
            </Button>
          </Link>
          <Link href={`/manager/plans/new?client_id=${params.id}`}>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Создать план
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Основная информация</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-2">
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">ФИО:</dt>
                <dd className="col-span-2">{client.full_name}</dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Дата рождения:</dt>
                <dd className="col-span-2">
                  {client.birth_date ? format(new Date(client.birth_date), "dd MMMM yyyy", { locale: ru }) : "-"}
                </dd>
              </div>
              <div className="grid grid-cols-3 gap-1">
                <dt className="font-medium text-gray-500">Активный план:</dt>
                <dd className="col-span-2">
                  {activePlan ? (
                    <Link href={`/manager/plans/${activePlan.id}`} className="text-blue-600 hover:underline">
                      {activePlan.name}
                    </Link>
                  ) : (
                    <span className="text-gray-500">Нет активного плана</span>
                  )}
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Документы</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              <li className="flex items-center">
                <span className={`w-5 h-5 rounded-full mr-2 ${client.has_oms ? "bg-green-500" : "bg-gray-300"}`}></span>
                <span>Копия ОМС</span>
              </li>
              <li className="flex items-center">
                <span className={`w-5 h-5 rounded-full mr-2 ${client.has_dms ? "bg-green-500" : "bg-gray-300"}`}></span>
                <span>Копия ДМС</span>
              </li>
              <li className="flex items-center">
                <span
                  className={`w-5 h-5 rounded-full mr-2 ${client.has_snils ? "bg-green-500" : "bg-gray-300"}`}
                ></span>
                <span>Копия СНИЛС</span>
              </li>
              <li className="flex items-center">
                <span
                  className={`w-5 h-5 rounded-full mr-2 ${client.has_disability_certificate ? "bg-green-500" : "bg-gray-300"}`}
                ></span>
                <span>Копия справки об инвалидности</span>
              </li>
            </ul>

            {client.medical_documents && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">Медицинские документы:</h4>
                <p className="text-sm whitespace-pre-line">{client.medical_documents}</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Медицинская информация</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-2">
              {client.doctor_name && (
                <div className="grid grid-cols-3 gap-1">
                  <dt className="font-medium text-gray-500">Лечащий врач:</dt>
                  <dd className="col-span-2">{client.doctor_name}</dd>
                </div>
              )}
              {client.doctor_phone && (
                <div className="grid grid-cols-3 gap-1">
                  <dt className="font-medium text-gray-500">Телефон врача:</dt>
                  <dd className="col-span-2">{client.doctor_phone}</dd>
                </div>
              )}
              {client.social_worker_name && (
                <div className="grid grid-cols-3 gap-1">
                  <dt className="font-medium text-gray-500">Соц. работник:</dt>
                  <dd className="col-span-2">{client.social_worker_name}</dd>
                </div>
              )}
              {client.social_worker_phone && (
                <div className="grid grid-cols-3 gap-1">
                  <dt className="font-medium text-gray-500">Телефон соц. работника:</dt>
                  <dd className="col-span-2">{client.social_worker_phone}</dd>
                </div>
              )}
              {client.state_clinic_address && (
                <div className="grid grid-cols-3 gap-1">
                  <dt className="font-medium text-gray-500">Гос. поликлиника:</dt>
                  <dd className="col-span-2">{client.state_clinic_address}</dd>
                </div>
              )}
              {client.state_clinic_phone && (
                <div className="grid grid-cols-3 gap-1">
                  <dt className="font-medium text-gray-500">Телефон поликлиники:</dt>
                  <dd className="col-span-2">{client.state_clinic_phone}</dd>
                </div>
              )}
              {client.commercial_clinic_address && (
                <div className="grid grid-cols-3 gap-1">
                  <dt className="font-medium text-gray-500">Комм. клиника:</dt>
                  <dd className="col-span-2">{client.commercial_clinic_address}</dd>
                </div>
              )}
              {client.commercial_clinic_phone && (
                <div className="grid grid-cols-3 gap-1">
                  <dt className="font-medium text-gray-500">Телефон комм. клиники:</dt>
                  <dd className="col-span-2">{client.commercial_clinic_phone}</dd>
                </div>
              )}
              {client.home_doctor_phone && (
                <div className="grid grid-cols-3 gap-1">
                  <dt className="font-medium text-gray-500">Врач на дом:</dt>
                  <dd className="col-span-2">{client.home_doctor_phone}</dd>
                </div>
              )}
              {client.emergency_service_phone && (
                <div className="grid grid-cols-3 gap-1">
                  <dt className="font-medium text-gray-500">Вызов СМП:</dt>
                  <dd className="col-span-2">{client.emergency_service_phone}</dd>
                </div>
              )}
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Аллергии и непереносимости</CardTitle>
          </CardHeader>
          <CardContent>
            {client.medication_allergies && (
              <div className="mb-4">
                <h4 className="font-medium mb-1">На лекарства:</h4>
                <p className="text-sm whitespace-pre-line">{client.medication_allergies}</p>
              </div>
            )}
            {client.food_allergies && (
              <div className="mb-4">
                <h4 className="font-medium mb-1">На продукты питания:</h4>
                <p className="text-sm whitespace-pre-line">{client.food_allergies}</p>
              </div>
            )}
            {client.other_allergies && (
              <div className="mb-4">
                <h4 className="font-medium mb-1">Другие:</h4>
                <p className="text-sm whitespace-pre-line">{client.other_allergies}</p>
              </div>
            )}
            {!client.medication_allergies && !client.food_allergies && !client.other_allergies && (
              <p className="text-gray-500">Информация об аллергиях не указана</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Родственники/опекуны/персонал</CardTitle>
          </CardHeader>
          <CardContent>
            {relatives && relatives.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">ФИО</th>
                      <th className="text-left py-2">Степень родства</th>
                      <th className="text-left py-2">Контакты</th>
                      <th className="text-center py-2">Орг. вопросы</th>
                      <th className="text-center py-2">Проживает</th>
                    </tr>
                  </thead>
                  <tbody>
                    {relatives.map((relative) => (
                      <tr key={relative.id} className="border-b">
                        <td className="py-2">{relative.full_name}</td>
                        <td className="py-2">{relative.relationship}</td>
                        <td className="py-2">{relative.contact_phone}</td>
                        <td className="py-2 text-center">{relative.handles_organizational_matters ? "✓" : ""}</td>
                        <td className="py-2 text-center">{relative.lives_with_client ? "✓" : ""}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-gray-500">Родственники не указаны</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Бытовая информация</CardTitle>
          </CardHeader>
          <CardContent>
            {client.management_company_phone && (
              <div className="mb-4">
                <h4 className="font-medium mb-1">Управляющая компания:</h4>
                <p className="text-sm">{client.management_company_phone}</p>
              </div>
            )}
            {client.other_household_services && (
              <div className="mb-4">
                <h4 className="font-medium mb-1">Другие бытовые услуги:</h4>
                <p className="text-sm whitespace-pre-line">{client.other_household_services}</p>
              </div>
            )}
            {client.product_procurement && (
              <div className="mb-4">
                <h4 className="font-medium mb-1">Закупка продуктов:</h4>
                <p className="text-sm whitespace-pre-line">{client.product_procurement}</p>
              </div>
            )}
            {client.medication_procurement && (
              <div className="mb-4">
                <h4 className="font-medium mb-1">Закупка лекарств:</h4>
                <p className="text-sm whitespace-pre-line">{client.medication_procurement}</p>
              </div>
            )}
            {client.pets && (
              <div className="mb-4">
                <h4 className="font-medium mb-1">Домашние питомцы:</h4>
                <p className="text-sm">{client.pets}</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Пищевые предпочтения</CardTitle>
          </CardHeader>
          <CardContent>
            {client.habits && (
              <div className="mb-4">
                <h4 className="font-medium mb-1">Привычки:</h4>
                <p className="text-sm whitespace-pre-line">{client.habits}</p>
              </div>
            )}
            {client.food_preferences && (
              <div className="mb-4">
                <h4 className="font-medium mb-1">Пищевые предпочтения:</h4>
                <p className="text-sm whitespace-pre-line">{client.food_preferences}</p>
              </div>
            )}
            {client.food_exclusions && (
              <div className="mb-4">
                <h4 className="font-medium mb-1">Пищевые исключения:</h4>
                <p className="text-sm whitespace-pre-line">{client.food_exclusions}</p>
              </div>
            )}
            {client.doctor_recommended_diet && (
              <div className="mb-4">
                <h4 className="font-medium mb-1">Диета, рекомендованная врачом:</h4>
                <p className="text-sm whitespace-pre-line">{client.doctor_recommended_diet}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
