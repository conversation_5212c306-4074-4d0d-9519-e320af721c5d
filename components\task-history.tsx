"use client"

import { useState, useEffect, useCallback } from "react"
import { createClient } from "@/lib/supabase/client"
import { useAuth } from "@/components/auth-provider"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Loader2, AlertCircle, RefreshCw } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"

interface TaskHistoryItem {
  id: string
  task_id: string
  action: string
  details: any
  created_at: string
  task?: {
    text: string
  } | null
}

export function TaskHistory() {
  const [history, setHistory] = useState<TaskHistoryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const { user, refreshSession } = useAuth()
  const { toast } = useToast()
  const supabase = createClient()

  // Функция для загрузки истории
  const fetchHistory = useCallback(async () => {
    if (!user) {
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)

    try {
      // Упрощенный запрос без связи с задачами
      const { data, error } = await supabase
        .from("task_history")
        .select("*")
        .order("created_at", { ascending: false })
        .limit(20)

      if (error) throw error

      // Получаем тексты задач отдельным запросом
      const taskIds = data?.map((item) => item.task_id) || []
      let taskTexts: Record<string, string> = {}

      if (taskIds.length > 0) {
        const { data: tasksData } = await supabase.from("tasks").select("id, text").in("id", taskIds)

        if (tasksData) {
          taskTexts = tasksData.reduce((acc: Record<string, string>, task: any) => {
            acc[task.id] = task.text
            return acc
          }, {})
        }
      }

      // Объединяем данные
      const historyWithTaskText =
        data?.map((item) => ({
          ...item,
          task: taskTexts[item.task_id] ? { text: taskTexts[item.task_id] } : null,
        })) || []

      setHistory(historyWithTaskText)
    } catch (err: any) {
      console.error("Ошибка при загрузке истории:", err)
      setError(err.message || "Не удалось загрузить историю действий")

      // Если ошибка связана с аутентификацией, пытаемся обновить сессию
      if (err.message?.includes("auth") || err.message?.includes("JWT")) {
        await refreshSession()
      }

      toast({
        title: "Ошибка загрузки",
        description: err.message || "Не удалось загрузить историю действий",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }, [user, toast, supabase, refreshSession])

  // Функция для обновления истории
  const refreshHistory = async () => {
    setIsRefreshing(true)
    try {
      await fetchHistory()
      toast({
        title: "История обновлена",
        description: "История действий успешно обновлена",
      })
    } catch (err) {
      console.error("Ошибка при обновлении истории:", err)
    } finally {
      setIsRefreshing(false)
    }
  }

  useEffect(() => {
    fetchHistory()

    // Обновляем историю при фокусе на странице
    const handleFocus = () => {
      fetchHistory()
    }

    window.addEventListener("focus", handleFocus)
    return () => {
      window.removeEventListener("focus", handleFocus)
    }
  }, [fetchHistory])

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return new Intl.DateTimeFormat("ru-RU", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      }).format(date)
    } catch (e) {
      return dateString
    }
  }

  const getActionText = (action: string, details: any) => {
    try {
      switch (action) {
        case "create":
          return "Создана задача"
        case "update":
          if (details?.completed === true) return "Задача отмечена как выполненная"
          if (details?.completed === false && details?.status === "pending") return "Задача отмечена как активная"
          if (details?.status === "cancelled") return "Задача отменена"
          if (details?.comment) return "Обновлен комментарий к задаче"
          return "Задача обновлена"
        case "delete":
          return "Задача удалена"
        case "add_tag":
          return `Добавлен тег "${details?.tag_name || ""}"`
        case "remove_tag":
          return `Удален тег "${details?.tag_name || ""}"`
        default:
          return "Действие с задачей"
      }
    } catch (e) {
      return "Действие с задачей"
    }
  }

  const getActionColor = (action: string) => {
    switch (action) {
      case "create":
        return "bg-green-100 text-green-800"
      case "update":
        return "bg-blue-100 text-blue-800"
      case "delete":
        return "bg-red-100 text-red-800"
      case "add_tag":
        return "bg-purple-100 text-purple-800"
      case "remove_tag":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (error) {
    return (
      <div className="space-y-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button onClick={refreshHistory} disabled={isRefreshing} className="w-full">
          {isRefreshing ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <RefreshCw className="mr-2 h-4 w-4" />}
          Попробовать снова
        </Button>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Загрузка истории...</span>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>История действий</CardTitle>
          <CardDescription>Последние действия с задачами</CardDescription>
        </div>
        <Button variant="outline" size="icon" onClick={refreshHistory} disabled={isRefreshing}>
          <RefreshCw className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
          <span className="sr-only">Обновить</span>
        </Button>
      </CardHeader>
      <CardContent>
        {history.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">История пуста</div>
        ) : (
          <div className="space-y-4">
            {history.map((item) => (
              <div key={item.id} className="border-b pb-3 last:border-0">
                <div className="flex justify-between items-start">
                  <div>
                    <Badge className={getActionColor(item.action)}>{getActionText(item.action, item.details)}</Badge>
                    <div className="mt-1 font-medium">{item.task?.text || "Задача удалена"}</div>
                  </div>
                  <div className="text-xs text-muted-foreground">{formatDate(item.created_at)}</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
