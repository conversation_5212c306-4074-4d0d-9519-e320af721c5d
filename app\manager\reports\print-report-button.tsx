"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Printer } from "lucide-react"
import type { RefObject } from "react"
import { jsPDF } from "jspdf"
import html2canvas from "html2canvas"

interface PrintReportButtonProps {
  reportRef: RefObject<HTMLDivElement>
  title: string
}

export function PrintReportButton({ reportRef, title }: PrintReportButtonProps) {
  const handlePrint = () => {
    if (reportRef.current) {
      window.print()
    }
  }

  const handleExportPDF = async () => {
    if (!reportRef.current) return

    try {
      const canvas = await html2canvas(reportRef.current, {
        scale: 2,
        logging: false,
        useCORS: true,
      })

      const imgData = canvas.toDataURL("image/png")
      const pdf = new jsPDF({
        orientation: "landscape",
        unit: "mm",
      })

      const imgWidth = 280
      const imgHeight = (canvas.height * imgWidth) / canvas.width

      pdf.addImage(imgData, "PNG", 10, 10, imgWidth, imgHeight)
      pdf.save(`${title.toLowerCase().replace(/\s+/g, "-")}.pdf`)
    } catch (error) {
      console.error("Error exporting PDF:", error)
      alert("Ошибка при экспорте в PDF. Попробуйте еще раз.")
    }
  }

  return (
    <div className="flex space-x-2">
      <Button variant="outline" onClick={handlePrint}>
        <Printer className="mr-2 h-4 w-4" /> Печать
      </Button>
      <Button onClick={handleExportPDF}>
        <Printer className="mr-2 h-4 w-4" /> Экспорт в PDF
      </Button>
    </div>
  )
}
