"use client"

import type React from "react"

import { useState, useEffect, useC<PERSON>back, useRef, useMemo } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Trash,
  Menu,
  LogOut,
  RefreshCw,
  AlertCircle,
  Calendar,
  X,
  Check,
  Layers,
  Info,
  PlusCircle,
  History,
} from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle } from "@/components/ui/drawer"
import { useMediaQuery } from "@/hooks/use-media-query"
import { useAuth } from "@/components/auth-provider"
import type { Task, TaskInterval, TaskGroup } from "@/lib/actions"
import { updateTask, deleteTask, getTasks } from "@/lib/actions"
import { useToast } from "@/hooks/use-toast"
import { format } from "date-fns"
import { ru } from "date-fns/locale"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"

// Иконки для групп задач
const groupIcons: Record<string, React.ReactNode> = {
  shower: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-shower-head"
    >
      <path d="m4 4 2.5 2.5" />
      <path d="M13.5 6.5a4.95 4.95 0 0 0-7 7" />
      <path d="M15 5 5 15" />
      <path d="M14 17v.01" />
      <path d="M10 16v.01" />
      <path d="M13 13v.01" />
      <path d="M16 10v.01" />
      <path d="M11 20v.01" />
      <path d="M17 14v.01" />
      <path d="M20 11v.01" />
    </svg>
  ),
  eye: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-eye"
    >
      <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z" />
      <circle cx="12" cy="12" r="3" />
    </svg>
  ),
  pill: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-pill"
    >
      <path d="m10.5 20.5 10-10a4.95 4.95 0 1 0-7-7l-10 10a4.95 4.95 0 1 0 7 7Z" />
      <path d="m8.5 8.5 7 7" />
    </svg>
  ),
  utensils: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-utensils"
    >
      <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2" />
      <path d="M7 2v20" />
      <path d="M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Z" />
    </svg>
  ),
  activity: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-activity"
    >
      <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
    </svg>
  ),
  trash: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-trash"
    >
      <path d="M3 6h18" />
      <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
      <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
    </svg>
  ),
}

// Иконки для статусов задач
const statusIcons = {
  all: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-list"
    >
      <line x1="8" x2="21" y1="6" y2="6" />
      <line x1="8" x2="21" y1="12" y2="12" />
      <line x1="8" x2="21" y1="18" y2="18" />
      <line x1="3" x2="3.01" y1="6" y2="6" />
      <line x1="3" x2="3.01" y1="12" y2="12" />
      <line x1="3" x2="3.01" y1="18" y2="18" />
    </svg>
  ),
  completed: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-check-circle"
    >
      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
      <path d="m9 11 3 3L22 4" />
    </svg>
  ),
  pending: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-circle"
    >
      <circle cx="12" cy="12" r="10" />
    </svg>
  ),
}

interface TodoListProps {
  initialTasks: Task[]
  initialIntervals: TaskInterval[]
  initialGroups: TaskGroup[]
}

export default function TodoListV2({ initialTasks, initialIntervals, initialGroups }: TodoListProps) {
  const [tasks, setTasks] = useState<Task[]>(initialTasks)
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [activeIntervalTab, setActiveIntervalTab] = useState<string | null>(null)
  const [activeGroupTab, setActiveGroupTab] = useState<string | null>(null)
  const [activeStatusTab, setActiveStatusTab] = useState<string>("all")
  const [selectedTask, setSelectedTask] = useState<Task | null>(null)
  const [taskComment, setTaskComment] = useState("")
  const [isTaskDialogOpen, setIsTaskDialogOpen] = useState(false)
  const [isCommentRequired, setIsCommentRequired] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const isMobile = useMediaQuery("(max-width: 640px)")
  const { user, signOut } = useAuth()
  const { toast } = useToast()
  const intervals = initialIntervals
  const groups = initialGroups
  const commentInputRef = useRef<HTMLTextAreaElement>(null)

  // Добавим состояние для попапа информации о клиенте
  const [isClientInfoOpen, setIsClientInfoOpen] = useState(false)
  const [isCalendarOpen, setIsCalendarOpen] = useState(false)
  const [isConfirmResetOpen, setIsConfirmResetOpen] = useState(false)

  // For admin panel visibility
  const [adminPanelVisible, setAdminPanelVisible] = useState(false)
  const [adminClickCount, setAdminClickCount] = useState(0)
  const [adminClickTimer, setAdminClickTimer] = useState<NodeJS.Timeout | null>(null)

  // Function to handle clicks on the menu area for admin panel activation
  const handleMenuAreaClick = useCallback(() => {
    setAdminClickCount((prevCount) => prevCount + 1)

    // Reset click counter after a delay
    if (adminClickTimer) {
      clearTimeout(adminClickTimer)
    }

    const timer = setTimeout(() => {
      // If double click (2 clicks), show admin panel
      if (adminClickCount + 1 === 2) {
        setAdminPanelVisible(true)
        toast({
          title: "Панель администратора",
          description: "Доступ к панели администратора разблокирован",
        })
      }
      setAdminClickCount(0)
    }, 300) // 300ms window for double-click

    setAdminClickTimer(timer)
  }, [adminClickCount, adminClickTimer, toast])

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (adminClickTimer) {
        clearTimeout(adminClickTimer)
      }
    }
  }, [adminClickTimer])

  // Функция для обновления задач
  const refreshTasks = useCallback(async () => {
    setIsRefreshing(true)
    try {
      const formattedDate = format(selectedDate, "yyyy-MM-dd")
      const { tasks: updatedTasks } = await getTasks(formattedDate)
      setTasks(updatedTasks)
      setError(null)
    } catch (err: any) {
      console.error("Ошибка обновления задач:", err)
      setError(err.message || "Не удалось обновить список задач")
    } finally {
      setIsRefreshing(false)
    }
  }, [selectedDate])

  // Обновляем задачи при изменении даты
  useEffect(() => {
    refreshTasks()
  }, [selectedDate, refreshTasks])

  // Функция для определения интервала по времени
  const getIntervalByTime = useCallback(
    (time: string | null): string | null => {
      if (!time) return null

      for (const interval of intervals) {
        const startTime = interval.start_time
        const endTime = interval.end_time

        // Проверяем, попадает ли время в интервал (включая крайние значения)
        if (time >= startTime && time <= endTime) {
          return interval.id
        }
      }

      return null
    },
    [intervals],
  )

  // Фильтрация задач
  const filteredTasks = useMemo(() => {
    return tasks.filter((task) => {
      // Фильтр по статусу
      if (activeStatusTab === "completed" && !task.completed) return false
      if (activeStatusTab === "pending" && task.completed) return false

      // Фильтр по группе
      if (activeGroupTab && task.group_id !== activeGroupTab) return false

      // Фильтр по интервалу
      if (activeIntervalTab) {
        // Если задача имеет указанный интервал
        if (task.interval_id === activeIntervalTab) return true

        // Если задача не имеет интервала, но имеет время, которое попадает в выбранный интервал
        if (!task.interval_id && task.execution_time) {
          const timeIntervalId = getIntervalByTime(task.execution_time)
          return timeIntervalId === activeIntervalTab
        }

        return false
      }

      return true
    })
  }, [tasks, activeStatusTab, activeIntervalTab, activeGroupTab, getIntervalByTime])

  // Сортировка интервалов от раннего к позднему
  const sortedIntervals = useMemo(() => {
    return [...intervals].sort((a, b) => {
      return a.start_time.localeCompare(b.start_time)
    })
  }, [intervals])

  // Группировка задач по интервалам с учетом времени выполнения
  const tasksByInterval = useMemo(() => {
    if (activeIntervalTab) {
      return { [activeIntervalTab]: filteredTasks }
    }

    return filteredTasks.reduce<Record<string, Task[]>>((acc, task) => {
      // Определяем интервал для задачи
      let intervalId = task.interval_id

      // Если интервал не указан, но есть время выполнения, определяем интервал по времени
      if (!intervalId && task.execution_time) {
        intervalId = getIntervalByTime(task.execution_time)
      }

      // Если интервал не определен, используем специальный ключ
      const key = intervalId || "no-interval"

      if (!acc[key]) {
        acc[key] = []
      }

      acc[key].push(task)
      return acc
    }, {})
  }, [filteredTasks, activeIntervalTab, getIntervalByTime])

  // Сортировка задач внутри каждого интервала
  useEffect(() => {
    Object.keys(tasksByInterval).forEach((intervalId) => {
      tasksByInterval[intervalId].sort((a, b) => {
        // Задачи без времени всегда идут выше задач с временем
        if (!a.execution_time && !b.execution_time) {
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        }

        if (!a.execution_time) return -1
        if (!b.execution_time) return 1

        // Сортировка задач с временем по времени выполнения
        return a.execution_time.localeCompare(b.execution_time)
      })
    })
  }, [tasksByInterval])

  // Добавим функцию для сброса статуса задачи
  const resetTaskStatus = useCallback(
    async (taskId: string) => {
      try {
        const { task, error } = await updateTask(taskId, {
          status: "pending",
          completed: false,
        })

        if (error) {
          throw new Error(error)
        }

        // Обновляем задачу в списке
        setTasks(tasks.map((t) => (t.id === taskId ? { ...t, status: "pending", completed: false } : t)))
        setIsConfirmResetOpen(false)
        setIsTaskDialogOpen(false)

        toast({
          title: "Статус сброшен",
          description: "Задача возвращена в активное состояние",
        })
      } catch (err: any) {
        toast({
          title: "Ошибка",
          description: err.message,
          variant: "destructive",
        })
      }
    },
    [tasks, toast],
  )

  // Заменим функцию открытия диалога задачи
  const openTaskDialog = useCallback((task: Task) => {
    setSelectedTask(task)
    setTaskComment(task.comment || "")
    setIsCommentRequired(false)

    // Проверяем статус задачи и устанавливаем isConfirmResetOpen только для неактивных задач
    const isTaskInactive = task.status === "completed" || task.status === "cancelled"
    setIsConfirmResetOpen(isTaskInactive)

    setIsTaskDialogOpen(true)
  }, [])

  // Обработчик закрытия диалога
  const handleDialogClose = useCallback(() => {
    setIsTaskDialogOpen(false)
    setIsConfirmResetOpen(false)
  }, [])

  // Сохранить комментарий к задаче
  const saveTaskComment = useCallback(
    async (status?: "pending" | "completed" | "cancelled") => {
      if (!selectedTask) return

      // Проверка на обязательный комментарий при отмене задачи
      if (status === "cancelled" && !taskComment.trim()) {
        setIsCommentRequired(true)
        return
      }

      try {
        const updates: any = { comment: taskComment }

        // Если передан статус, обновляем его
        if (status) {
          updates.status = status
          updates.completed = status === "completed"
        }

        const { task, error } = await updateTask(selectedTask.id, updates)

        if (error) {
          throw new Error(error)
        }

        // Обновляем задачу в списке
        setTasks(tasks.map((t) => (t.id === selectedTask.id ? { ...t, ...updates } : t)))

        // Закрываем диалог только если это не отмена задачи с пустым комментарием
        if (!(status === "cancelled" && !taskComment.trim())) {
          setIsTaskDialogOpen(false)
        }
      } catch (err: any) {
        toast({
          title: "Ошибка",
          description: err.message,
          variant: "destructive",
        })
      }
    },
    [selectedTask, taskComment, tasks, toast],
  )

  // Удалить задачу
  const handleRemoveTask = useCallback(
    async (id: string) => {
      try {
        const { error, success } = await deleteTask(id)

        if (error) {
          throw new Error(error)
        }

        if (success) {
          setTasks(tasks.filter((task) => task.id !== id))
          setIsTaskDialogOpen(false)
        }
      } catch (err: any) {
        toast({
          title: "Ошибка",
          description: err.message,
          variant: "destructive",
        })
      }
    },
    [tasks, toast],
  )

  // Форматирование времени
  const formatTime = useCallback((timeString: string | null) => {
    if (!timeString) return ""
    return timeString.substring(0, 5) // Формат HH:MM
  }, [])

  // Получение названия интервала по ID
  const getIntervalName = useCallback(
    (id: string | null) => {
      if (!id) return "" // Changed from "Без интервала" to empty string
      const interval = intervals.find((i) => i.id === id)
      return interval ? interval.name : "" // Changed from "Без интервала" to empty string
    },
    [intervals],
  )

  // Получение иконки группы по ID
  const getGroupIcon = useCallback(
    (id: string | null) => {
      if (!id) return null
      const group = groups.find((g) => g.id === id)
      return group && group.icon ? groupIcons[group.icon] : null
    },
    [groups],
  )

  // Получение названия группы по ID
  const getGroupName = useCallback(
    (id: string | null) => {
      if (!id) return "Без группы"
      const group = groups.find((g) => g.id === id)
      return group ? group.name : "Без группы"
    },
    [groups],
  )

  // Проверка, является ли задача созданной текущим пользователем
  const isTaskCreatedByCurrentUser = useCallback(
    (task: Task) => {
      return task.created_by === user?.id
    },
    [user],
  )

  // Получаем отсортированные ключи интервалов
  const sortedIntervalKeys = useMemo(() => {
    return Object.keys(tasksByInterval).sort((a, b) => {
      // Специальный ключ "no-interval" всегда в конце
      if (a === "no-interval") return 1
      if (b === "no-interval") return -1

      // Получаем интервалы по ID
      const intervalA = intervals.find((i) => i.id === a)
      const intervalB = intervals.find((i) => i.id === b)

      // Если интервал не найден, считаем его временем "00:00"
      const timeA = intervalA ? intervalA.start_time : "00:00"
      const timeB = intervalB ? intervalB.start_time : "00:00"

      // Сравниваем времена начала интервалов
      return timeA.localeCompare(timeB)
    })
  }, [tasksByInterval, intervals])

  // Мемоизируем содержимое диалога задачи
  const TaskDetailsComponent = useMemo(() => {
    if (!selectedTask) return null

    // Проверяем, активна ли задача
    const isTaskActive = selectedTask.status !== "completed" && selectedTask.status !== "cancelled"

    const content = (
      <>
        {isConfirmResetOpen && (
          <div className="mb-4 p-3 bg-muted rounded-md">
            <p className="text-sm mb-2">
              {selectedTask.status === "completed"
                ? "Задача отмечена как выполненная. Хотите вернуть её в активное состояние?"
                : "Задача отменена. Хотите вернуть её в активное состояние?"}
            </p>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setIsConfirmResetOpen(false)
                  setIsTaskDialogOpen(false)
                }}
              >
                Нет
              </Button>
              <Button variant="default" size="sm" onClick={() => resetTaskStatus(selectedTask.id)}>
                Да, вернуть
              </Button>
            </div>
          </div>
        )}

        <div className="grid gap-4">
          <div>
            <p className="text-sm font-medium mb-1">Группа</p>
            <div className="flex items-center gap-2">
              {getGroupIcon(selectedTask.group_id)}
              <span>{getGroupName(selectedTask.group_id)}</span>
            </div>
          </div>

          <div>
            <p className="text-sm font-medium mb-1">Интервал</p>
            <span>{getIntervalName(selectedTask.interval_id)}</span>
          </div>

          {selectedTask.execution_time && (
            <div>
              <p className="text-sm font-medium mb-1">Время выполнения</p>
              <span>{formatTime(selectedTask.execution_time)}</span>
            </div>
          )}

          <div className="grid gap-2">
            <label htmlFor="comment" className="text-sm font-medium">
              Комментарий {isCommentRequired && <span className="text-red-500">*</span>}
            </label>
            <textarea
              id="comment"
              ref={commentInputRef}
              rows={4}
              className={`w-full rounded-md border ${
                isCommentRequired ? "border-red-500" : "border-input"
              } bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2`}
              placeholder="Добавьте комментарий к задаче..."
              value={taskComment}
              onChange={(e) => {
                setTaskComment(e.target.value)
                if (isCommentRequired && e.target.value.trim()) {
                  setIsCommentRequired(false)
                }
              }}
              readOnly={!isTaskActive}
            />
            {isCommentRequired && <p className="text-xs text-red-500">Комментарий обязателен при отмене задачи</p>}
          </div>

          {isTaskActive && (
            <div className="border-t pt-4">
              <div className="flex justify-between items-center">
                <div className="text-sm font-medium">Статус задачи</div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => saveTaskComment("completed")}
                    className="text-green-600 hover:text-green-700 hover:bg-green-50"
                    title="Отметить как выполненную"
                  >
                    <Check className="h-4 w-4" />
                  </Button>

                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => saveTaskComment("cancelled")}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    title="Отменить задачу"
                  >
                    <X className="h-4 w-4" />
                  </Button>

                  {selectedTask.created_by === user?.id && (
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleRemoveTask(selectedTask.id)}
                      className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                      title="Удалить задачу"
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </>
    )

    if (isMobile) {
      return (
        <Drawer open={isTaskDialogOpen} onOpenChange={handleDialogClose}>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>{selectedTask.text}</DrawerTitle>
            </DrawerHeader>
            <div className="p-4">{content}</div>
          </DrawerContent>
        </Drawer>
      )
    }

    return (
      <Dialog open={isTaskDialogOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="sm:max-w-md" aria-describedby="task-dialog-description">
          <DialogHeader>
            <DialogTitle>{selectedTask.text}</DialogTitle>
            <DialogDescription id="task-dialog-description" className="sr-only">
              Детали задачи
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">{content}</div>
        </DialogContent>
      </Dialog>
    )
  }, [
    selectedTask,
    isConfirmResetOpen,
    isTaskDialogOpen,
    taskComment,
    isCommentRequired,
    isMobile,
    user,
    getGroupIcon,
    getGroupName,
    getIntervalName,
    formatTime,
    resetTaskStatus,
    saveTaskComment,
    handleRemoveTask,
    handleDialogClose,
  ])

  // Мемоизируем компонент информации о клиенте
  const ClientInfoDialog = useMemo(() => {
    // Моковые данные клиента
    const clientInfo = {
      fullName: "Иванов Иван Иванович",
      birthDate: "01.01.1980",
      phone: "+7 (999) 123-45-67",
      email: "<EMAIL>",
      address: "г. Москва, ул. Примерная, д. 1, кв. 1",
    }

    const content = (
      <div className="grid gap-4">
        <div>
          <p className="text-sm font-medium mb-1">ФИО</p>
          <p>{clientInfo.fullName}</p>
        </div>
        <div>
          <p className="text-sm font-medium mb-1">Дата рождения</p>
          <p>{clientInfo.birthDate}</p>
        </div>
        <div>
          <p className="text-sm font-medium mb-1">Телефон</p>
          <p>{clientInfo.phone}</p>
        </div>
        <div>
          <p className="text-sm font-medium mb-1">Email</p>
          <p>{clientInfo.email}</p>
        </div>
        <div>
          <p className="text-sm font-medium mb-1">Адрес</p>
          <p>{clientInfo.address}</p>
        </div>
      </div>
    )

    if (isMobile) {
      return (
        <Drawer open={isClientInfoOpen} onOpenChange={setIsClientInfoOpen}>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>Информация о клиенте</DrawerTitle>
            </DrawerHeader>
            <div className="p-4">{content}</div>
          </DrawerContent>
        </Drawer>
      )
    }

    return (
      <Dialog open={isClientInfoOpen} onOpenChange={setIsClientInfoOpen}>
        <DialogContent className="sm:max-w-md" aria-describedby="client-info-description">
          <DialogHeader>
            <DialogTitle>Информация о клиенте</DialogTitle>
            <DialogDescription id="client-info-description" className="sr-only">
              Информация о клиенте
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">{content}</div>
        </DialogContent>
      </Dialog>
    )
  }, [isClientInfoOpen, isMobile])

  // Заменим return блок
  return (
    <div className="flex flex-col min-h-[100dvh] bg-background">
      <header className="sticky top-0 z-10 bg-background border-b px-4 h-14 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <img src="/images/arina-logo.png" alt="Арина" className="h-8 w-8 rounded-full" />
          <h1 className="text-xl font-bold">Арина</h1>
          <Badge variant="outline">{format(selectedDate, "d MMMM", { locale: ru })}</Badge>
        </div>
        <div className="flex items-center gap-2">
          <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="icon">
                <Calendar className="h-5 w-5" />
                <span className="sr-only">Выбрать дату</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <CalendarComponent
                mode="single"
                selected={selectedDate}
                onSelect={(date) => {
                  if (date) {
                    setSelectedDate(date)
                    setIsCalendarOpen(false) // Закрываем календарь после выбора даты
                  }
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
          <Button variant="ghost" size="icon" onClick={refreshTasks} disabled={isRefreshing}>
            <RefreshCw className={`h-5 w-5 ${isRefreshing ? "animate-spin" : ""}`} />
            <span className="sr-only">Обновить</span>
          </Button>
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Меню</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <SheetHeader>
                <SheetTitle>Настройки</SheetTitle>
              </SheetHeader>
              <div className="py-4 space-y-4">
                <div className="bg-muted p-3 rounded-md">
                  <div className="text-sm font-medium mb-1">Пользователь</div>
                  <div className="text-sm text-muted-foreground break-all">{user?.email}</div>
                </div>

                <div
                  className="space-y-2"
                  // Add click handler to detect double-clicks in empty space
                  onClick={handleMenuAreaClick}
                >
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => (window.location.href = "/history")}
                  >
                    <History className="h-4 w-4 mr-2" />
                    История действий
                  </Button>

                  <Button variant="outline" className="w-full justify-start" onClick={() => setIsClientInfoOpen(true)}>
                    <Info className="h-4 w-4 mr-2" />
                    Информация о клиенте
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => (window.location.href = "/add-task")}
                  >
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Создать задачу
                  </Button>

                  {/* Show admin panel link only when activated */}
                  {adminPanelVisible && (
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => (window.location.href = "/admin")}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="lucide lucide-shield mr-2"
                      >
                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10" />
                      </svg>
                      Панель администратора
                    </Button>
                  )}

                  <Button variant="outline" className="w-full justify-start" onClick={() => signOut()}>
                    <LogOut className="h-4 w-4 mr-2" />
                    Выйти
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </header>

      <main className="flex-1 container max-w-3xl p-4">
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Фильтры по интервалам */}
        <Tabs
          value={activeIntervalTab || "all"}
          onValueChange={(value) => setActiveIntervalTab(value === "all" ? null : value)}
          className="w-full mb-4"
        >
          <TabsList className="flex overflow-x-auto pb-1 scrollbar-hide">
            <TabsTrigger value="all" className="flex items-center gap-1">
              <Layers className="h-4 w-4" />
              <span className="sr-only sm:not-sr-only sm:ml-1">Все</span>
            </TabsTrigger>
            {sortedIntervals.map((interval) => (
              <TabsTrigger key={interval.id} value={interval.id}>
                {interval.name}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>

        {/* Фильтры по статусу */}
        <div className="flex justify-center mb-4 gap-2 overflow-x-auto pb-1 scrollbar-hide">
          <Button
            variant={activeStatusTab === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => setActiveStatusTab("all")}
            className="flex items-center gap-1"
          >
            {statusIcons.all}
            <span className="sr-only sm:not-sr-only sm:ml-2">Все</span>
          </Button>
          <Button
            variant={activeStatusTab === "completed" ? "default" : "outline"}
            size="sm"
            onClick={() => setActiveStatusTab("completed")}
            className="flex items-center gap-1"
          >
            {statusIcons.completed}
            <span className="sr-only sm:not-sr-only sm:ml-2">Выполненные</span>
          </Button>
          <Button
            variant={activeStatusTab === "pending" ? "default" : "outline"}
            size="sm"
            onClick={() => setActiveStatusTab("pending")}
            className="flex items-center gap-1"
          >
            {statusIcons.pending}
            <span className="sr-only sm:not-sr-only sm:ml-2">Невыполненные</span>
          </Button>
        </div>

        {/* Список задач */}
        <div className="space-y-6 mb-20">
          {sortedIntervalKeys.map((intervalId) => {
            const intervalTasks = tasksByInterval[intervalId]

            // Пропускаем пустые интервалы
            if (intervalTasks.length === 0) return null

            // Получаем название интервала
            const intervalName = intervalId === "no-interval" ? "Без интервала" : getIntervalName(intervalId)

            return (
              <div key={intervalId} className="space-y-2">
                {!activeIntervalTab && intervalId !== "no-interval" && (
                  <h2 className="text-lg font-semibold border-b pb-1">{intervalName}</h2>
                )}

                {intervalTasks.map((task) => (
                  <div
                    key={task.id}
                    className={`flex flex-col rounded-lg border p-4 shadow-sm transition-shadow hover:shadow-md active:bg-muted/30 ${
                      task.status === "completed"
                        ? "bg-muted/50"
                        : task.status === "cancelled"
                          ? "bg-red-100/30"
                          : isTaskCreatedByCurrentUser(task)
                            ? "bg-blue-50/30"
                            : ""
                    }`}
                    onClick={() => openTaskDialog(task)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {task.status === "completed" ? (
                          <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center">
                            <Check className="h-3 w-3 text-white" />
                          </div>
                        ) : task.status === "cancelled" ? (
                          <div className="w-5 h-5 rounded-full bg-red-500 flex items-center justify-center">
                            <X className="h-3 w-3 text-white" />
                          </div>
                        ) : (
                          <div className="w-5 h-5 rounded-full border-2 border-gray-300"></div>
                        )}
                        <span
                          className={`font-medium ${
                            task.status === "completed"
                              ? "line-through text-muted-foreground"
                              : task.status === "cancelled"
                                ? "line-through text-red-500"
                                : ""
                          }`}
                        >
                          {task.text}
                        </span>
                      </div>

                      <div className="flex items-center">
                        {task.execution_time && (
                          <Badge variant="outline" className="mr-2">
                            {formatTime(task.execution_time)}
                          </Badge>
                        )}
                        {getGroupIcon(task.group_id) && (
                          <div className="text-muted-foreground" title={getGroupName(task.group_id)}>
                            {getGroupIcon(task.group_id)}
                          </div>
                        )}
                      </div>
                    </div>

                    {task.comment && (
                      <div className="mt-2 text-sm text-muted-foreground">
                        <p className="line-clamp-1">{task.comment}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )
          })}

          {filteredTasks.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">Нет задач для отображения</div>
          )}
        </div>
      </main>

      {/* Нижняя панель с фильтрами по группам */}
      <footer className="fixed bottom-0 left-0 right-0 z-10 bg-background border-t overflow-x-auto">
        <div className="flex items-center p-2 min-w-max justify-center">
          <Button
            variant={activeGroupTab === null ? "default" : "ghost"}
            size="sm"
            className="flex flex-col items-center gap-1"
            onClick={() => setActiveGroupTab(null)}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="lucide lucide-layers"
            >
              <path d="m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z" />
              <path d="m22 12.5-8.58 3.91a2 2 0 0 1-1.66 0L2.6 12.5" />
              <path d="m22 17.5-8.58 3.91a2 2 0 0 1-1.66 0L2.6 17.5" />
            </svg>
            <span className="text-xs">Все</span>
          </Button>

          {groups.map((group) => (
            <Button
              key={group.id}
              variant={activeGroupTab === group.id ? "default" : "ghost"}
              size="sm"
              className="flex flex-col items-center gap-1"
              onClick={() => setActiveGroupTab(group.id)}
            >
              {group.icon && groupIcons[group.icon]}
              <span className="text-xs">{group.name}</span>
            </Button>
          ))}
        </div>
      </footer>

      {TaskDetailsComponent}
      {ClientInfoDialog}
    </div>
  )
}
