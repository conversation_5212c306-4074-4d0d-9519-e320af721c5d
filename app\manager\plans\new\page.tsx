import { createServerClient } from "@/lib/supabase/server"
import { PlanForm } from "../plan-form"
import { notFound } from "next/navigation"

export default async function NewPlanPage({
  searchParams,
}: { searchParams: { client_id?: string; default_plan_id?: string } }) {
  const supabase = await createServerClient()

  // Загружаем список всех клиентов
  const { data: clients, error: clientsError } = await supabase
    .from("clients")
    .select("id, full_name")
    .order("full_name", { ascending: true })

  if (clientsError) {
    console.error("Error loading clients:", clientsError)
  }

  let client = null
  let defaultPlan = null
  let defaultPlanItems = []

  // Если указан ID клиента, загружаем данные клиента
  if (searchParams.client_id) {
    const { data, error } = await supabase.from("clients").select("*").eq("id", searchParams.client_id).single()

    if (error) {
      notFound()
    }

    client = data
  }

  // Если указан ID дефолтного плана, загружаем его данные
  if (searchParams.default_plan_id) {
    const { data, error } = await supabase
      .from("default_plans")
      .select("*")
      .eq("id", searchParams.default_plan_id)
      .single()

    if (error) {
      notFound()
    }

    defaultPlan = data

    // Загружаем элементы дефолтного плана
    const { data: items, error: itemsError } = await supabase
      .from("default_plan_items")
      .select(`
        *,
        action:action_id(id, text, group_id),
        interval:interval_id(id, name)
      `)
      .eq("default_plan_id", searchParams.default_plan_id)
      .order("position", { ascending: true })

    if (!itemsError) {
      defaultPlanItems = items || []
    }
  }

  // Загружаем список действий
  const { data: actions, error: actionsError } = await supabase
    .from("actions")
    .select("*")
    .order("text", { ascending: true })

  if (actionsError) {
    console.error("Error loading actions:", actionsError)
  }

  // Загружаем список интервалов
  const { data: intervals, error: intervalsError } = await supabase
    .from("task_intervals")
    .select("*")
    .order("name", { ascending: true })

  if (intervalsError) {
    console.error("Error loading intervals:", intervalsError)
  }

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Создать новый план</h1>
      <PlanForm
        client={client}
        clients={clients || []}
        actions={actions || []}
        intervals={intervals || []}
        defaultPlan={defaultPlan}
        defaultPlanItems={defaultPlanItems}
      />
    </div>
  )
}
