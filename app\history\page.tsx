import { createServer<PERSON>lient } from "@/lib/supabase/server"
import { redirect } from "next/navigation"
import { TaskHistory } from "@/components/task-history"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export default async function HistoryPage() {
  // Проверяем наличие переменных окружения
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Ошибка конфигурации</h1>
        <p>
          Отсутствуют необходимые переменные окружения для Supabase. Убедитесь, что переменные NEXT_PUBLIC_SUPABASE_URL
          и NEXT_PUBLIC_SUPABASE_ANON_KEY настроены правильно.
        </p>
      </div>
    )
  }

  try {
    const supabase = await createServerClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      redirect("/")
    }

    return (
      <div className="container mx-auto max-w-3xl p-4 pb-20">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">История действий</h1>
          <Link href="/">
            <Button variant="outline">Назад к задачам</Button>
          </Link>
        </div>
        <TaskHistory />
      </div>
    )
  } catch (error) {
    console.error("Ошибка на странице истории:", error)
    return (
      <div className="container mx-auto max-w-3xl p-4">
        <h1 className="text-2xl font-bold mb-4">Ошибка загрузки</h1>
        <p className="mb-4">Произошла ошибка при загрузке истории действий.</p>
        <Link href="/">
          <Button>Вернуться на главную</Button>
        </Link>
      </div>
    )
  }
}
