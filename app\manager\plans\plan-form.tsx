"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Checkbox } from "@/components/ui/checkbox"
import { createClient } from "@/lib/supabase/client"
import { toast } from "@/hooks/use-toast"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { PlanItemsEditor } from "../default-plans/plan-items-editor"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Info } from "lucide-react"
import { <PERSON>, SelectContent, SelectItem, Select<PERSON>rigger, SelectValue } from "@/components/ui/select"

// Обновим схему формы, добавив поля для дат начала и окончания
const planFormSchema = z.object({
  name: z.string().min(2, { message: "Название должно содержать минимум 2 символа" }),
  description: z.string().optional(),
  is_active: z.boolean().default(false),
  start_date: z.date({ required_error: "Укажите дату начала" }),
  end_date: z.date({ required_error: "Укажите дату окончания" }),
  client_id: z.string().optional(),
})

type PlanFormValues = z.infer<typeof planFormSchema>

// Обновим интерфейс PlanFormProps, добавив clients
interface PlanFormProps {
  plan?: any // The plan data for editing
  client?: any // The client data (now optional)
  clients?: any[] // List of all clients for selection
  actions?: any[] // List of actions for selection
  intervals?: any[] // List of intervals for selection
  defaultPlan?: any // Default plan to use as template
  defaultPlanItems?: any[] // Default plan items to use as template
}

// Обновим функцию PlanForm, добавив параметр clients
export function PlanForm({
  plan,
  client,
  clients = [],
  actions = [],
  intervals = [],
  defaultPlan,
  defaultPlanItems = [],
}: PlanFormProps) {
  const [selectedClientId, setSelectedClientId] = useState<string | null>(client?.id || plan?.client_id || null)
  const [planItems, setPlanItems] = useState<any[]>([])
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
  const [isUsingDefaultPlan, setIsUsingDefaultPlan] = useState<boolean>(!!defaultPlan)
  const router = useRouter()
  const supabase = createClient()

  // Получим текущую дату и дату через месяц для значений по умолчанию
  const today = new Date()
  const nextMonth = new Date()
  nextMonth.setMonth(today.getMonth() + 1)

  // Обновим defaultValues, добавив даты начала и окончания
  const form = useForm<PlanFormValues>({
    resolver: zodResolver(planFormSchema),
    defaultValues: plan
      ? {
          ...plan,
          start_date: plan.start_date ? new Date(plan.start_date) : today,
          end_date: plan.end_date ? new Date(plan.end_date) : nextMonth,
          client_id: plan.client_id || "",
        }
      : defaultPlan
        ? {
            name: `${defaultPlan.name}${client ? ` - ${client.full_name}` : ""}`,
            description: defaultPlan.description || "",
            is_active: false,
            start_date: today,
            end_date: nextMonth,
            client_id: client?.id || "",
          }
        : {
            name: client ? `План для ${client.full_name}` : "Новый план",
            description: "",
            is_active: false,
            start_date: today,
            end_date: nextMonth,
            client_id: client?.id || "",
          },
  })

  // Load plan items if editing an existing plan
  useEffect(() => {
    const loadPlanItems = async () => {
      if (plan?.id) {
        const { data, error } = await supabase
          .from("plan_items")
          .select(
            `
            *,
            action:action_id(id, text, group_id),
            interval:interval_id(id, name)
          `,
          )
          .eq("plan_id", plan.id)
          .order("position", { ascending: true })

        if (error) {
          console.error("Error loading plan items:", error)
          toast({
            title: "Ошибка",
            description: "Не удалось загрузить элементы плана",
            variant: "destructive",
          })
        } else {
          setPlanItems(data || [])
        }
      } else if (defaultPlanItems && defaultPlanItems.length > 0) {
        // If we have default plan items, use them as initial items
        setPlanItems(
          defaultPlanItems.map((item) => ({
            ...item,
            id: undefined, // Remove ID so it will be created as new
            default_plan_id: undefined, // Remove default_plan_id
          })),
        )
      }
    }

    loadPlanItems()
  }, [plan, supabase, defaultPlanItems])

  // Обновим функцию onSubmit, чтобы она учитывала новые поля
  async function onSubmit(data: PlanFormValues) {
    setIsSubmitting(true)

    try {
      let planId = plan?.id

      // Проверим, что дата окончания не раньше даты начала
      if (data.end_date < data.start_date) {
        toast({
          title: "Ошибка",
          description: "Дата окончания не может быть раньше даты начала",
          variant: "destructive",
        })
        setIsSubmitting(false)
        return
      }

      // If this is a new active plan, we need to deactivate other plans for this client
      if (data.is_active && !plan?.id && data.client_id) {
        const { error: deactivateError } = await supabase
          .from("plans")
          .update({ is_active: false })
          .eq("client_id", data.client_id)
          .eq("is_active", true)

        if (deactivateError) {
          console.error("Error deactivating other plans:", deactivateError)
        }
      }

      // Форматируем даты для сохранения в базу данных
      const formattedData = {
        ...data,
        start_date: data.start_date.toISOString().split("T")[0],
        end_date: data.end_date.toISOString().split("T")[0],
      }

      if (planId) {
        // Update existing plan
        const { error } = await supabase.from("plans").update(formattedData).eq("id", planId)

        if (error) throw error

        toast({
          title: "План обновлен",
          description: "План успешно обновлен",
        })
      } else {
        // Create new plan
        const planData = {
          ...formattedData,
        }

        const { data: newPlan, error } = await supabase.from("plans").insert(planData).select()

        if (error) throw error

        if (newPlan && newPlan.length > 0) {
          planId = newPlan[0].id

          // If we're using a default plan, copy its items
          if (isUsingDefaultPlan && defaultPlanItems && defaultPlanItems.length > 0) {
            const planItemsToInsert = defaultPlanItems.map((item, index) => ({
              plan_id: planId,
              action_id: item.action_id,
              position: index,
              interval_id: item.interval_id,
              time: item.time,
              recurrence_type: item.recurrence_type,
              recurrence_value: item.recurrence_value,
              execution_date: item.recurrence_type === "once" ? formattedData.start_date : null,
            }))

            const { error: insertError } = await supabase.from("plan_items").insert(planItemsToInsert)

            if (insertError) {
              console.error("Error copying default plan items:", insertError)
              toast({
                title: "Предупреждение",
                description: "План создан, но возникла ошибка при копировании элементов дефолтного плана",
                variant: "destructive",
              })
            }
          }

          toast({
            title: "План создан",
            description: "Новый план успешно создан",
          })
        }
      }

      // Redirect to the plan detail page
      if (planId) {
        router.push(`/manager/plans/${planId}`)
      } else {
        router.push("/manager/plans")
      }
    } catch (error: any) {
      toast({
        title: "Ошибка",
        description: error.message || "Произошла ошибка при сохранении данных",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Tabs defaultValue="main" className="w-full">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="main">Основное</TabsTrigger>
            <TabsTrigger value="items" disabled={!plan?.id && !isUsingDefaultPlan}>
              Элементы плана
            </TabsTrigger>
          </TabsList>

          <TabsContent value="main" className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                {defaultPlan && (
                  <Alert className="mb-4">
                    <Info className="h-4 w-4" />
                    <AlertDescription>План создается на основе дефолтного плана "{defaultPlan.name}"</AlertDescription>
                  </Alert>
                )}

                {/* Добавим поля для дат начала и окончания, а также выбора клиента в форму
                Внутри CardContent в TabsContent с value="main" */}
                <div className="grid gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Название плана</FormLabel>
                        <FormControl>
                          <Input placeholder="Например: План ухода для Иванова И.И." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="client_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Клиент</FormLabel>
                        <Select
                          value={field.value || "none"}
                          onValueChange={(value) => {
                            field.onChange(value === "none" ? "" : value)
                            setSelectedClientId(value === "none" ? null : value)
                          }}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Выберите клиента" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="none">Без клиента</SelectItem>
                            {clients.map((client) => (
                              <SelectItem key={client.id} value={client.id}>
                                {client.full_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="start_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Дата начала</FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              value={field.value ? field.value.toISOString().split("T")[0] : ""}
                              onChange={(e) => {
                                const date = e.target.value ? new Date(e.target.value) : new Date()
                                field.onChange(date)
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="end_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Дата окончания</FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              value={field.value ? field.value.toISOString().split("T")[0] : ""}
                              onChange={(e) => {
                                const date = e.target.value ? new Date(e.target.value) : new Date()
                                field.onChange(date)
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Описание</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Добавьте описание плана"
                            className="min-h-[100px]"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="is_active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Активный план</FormLabel>
                          <p className="text-sm text-muted-foreground">
                            Если отмечено, этот план будет активным для клиента. У клиента может быть только один
                            активный план.
                          </p>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="items" className="space-y-4">
            {plan?.id || isUsingDefaultPlan ? (
              <PlanItemsEditor
                planId={plan?.id || "temp"}
                planItems={planItems}
                setPlanItems={setPlanItems}
                actions={actions}
                intervals={intervals}
                isDefaultPlan={false}
                planStartDate={form.getValues().start_date}
                planEndDate={form.getValues().end_date}
              />
            ) : (
              <Card>
                <CardContent className="pt-6">
                  <p className="text-center text-muted-foreground">Сначала сохраните план, чтобы добавить элементы</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()} disabled={isSubmitting}>
            Отмена
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Сохранение..." : plan ? "Обновить план" : "Создать план"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
