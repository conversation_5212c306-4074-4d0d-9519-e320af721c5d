import { getTasks, getIntervals, getGroups } from "@/lib/actions"
import { createServerClient } from "@/lib/supabase/server"
import TodoListV2 from "@/components/todo-list-v2"
import { AuthForm } from "@/components/auth-form"
import { EnvCheck } from "@/components/env-check"

export default async function Home() {
  try {
    const supabase = await createServerClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return (
        <div className="container mx-auto max-w-3xl p-4">
          <EnvCheck />
          <AuthForm />
        </div>
      )
    }

    const { tasks } = await getTasks()
    const { intervals } = await getIntervals()
    const { groups } = await getGroups()

    return (
      <div className="container mx-auto max-w-3xl p-4">
        <EnvCheck />
        <TodoListV2 initialTasks={tasks} initialIntervals={intervals} initialGroups={groups} />
      </div>
    )
  } catch (error) {
    console.error("Error in Home page:", error)
    return (
      <div className="container mx-auto max-w-3xl p-4">
        <EnvCheck />
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <h2 className="text-red-800 font-medium">Ошибка загрузки приложения</h2>
          <p className="text-red-700 mt-1">
            Произошла ошибка при загрузке приложения. Пожалуйста, попробуйте позже или обратитесь к администратору.
          </p>
        </div>
        <AuthForm />
      </div>
    )
  }
}
