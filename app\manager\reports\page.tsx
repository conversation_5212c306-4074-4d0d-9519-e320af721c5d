import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import Link from "next/link"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FileText } from "lucide-react"

export default function ReportsPage() {
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Отчеты</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Link href="/manager/reports/detailed">
          <Card className="h-full hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                Детальный отчет
              </CardTitle>
              <CardDescription>Подробная информация по всем задачам</CardDescription>
            </<PERSON><PERSON><PERSON>er>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Просмотр всех задач с возможностью фильтрации по клиентам, интервалам, группам и статусам. Включает
                комментарии и полную информацию о задачах.
              </p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/manager/reports/by-client">
          <Card className="h-full hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart className="mr-2 h-5 w-5" />
                Отчет по клиентам
              </CardTitle>
              <CardDescription>Статистика задач по клиентам</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Сводная статистика по задачам, сгруппированная по клиентам. Показывает количество задач в разных
                статусах для каждого клиента.
              </p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/manager/reports/by-status">
          <Card className="h-full hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader>
              <CardTitle className="flex items-center">
                <PieChart className="mr-2 h-5 w-5" />
                Отчет по статусам
              </CardTitle>
              <CardDescription>Распределение задач по статусам</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Анализ распределения задач по статусам с возможностью фильтрации по периоду, клиентам и другим
                параметрам.
              </p>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  )
}
