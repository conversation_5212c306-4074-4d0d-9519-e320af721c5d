"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { createClient } from "@/lib/supabase/client"
import { Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export function AuthForm() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("login")
  const [supabaseInitialized, setSupabaseInitialized] = useState(false)
  const { toast } = useToast()
  const [supabase, setSupabase] = useState<ReturnType<typeof createClient> | null>(null)

  // Инициализируем Supabase клиент
  useEffect(() => {
    try {
      const client = createClient()
      setSupabase(client)
      setSupabaseInitialized(true)
      console.log("Supabase client initialized successfully")
    } catch (err) {
      console.error("Failed to initialize Supabase client:", err)
      setError("Ошибка инициализации Supabase. Пожалуйста, попробуйте позже.")
    }
  }, [])

  // Проверяем переменные окружения
  useEffect(() => {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error("Missing Supabase environment variables")
      setError("Отсутствуют необходимые переменные окружения для Supabase")
    }
  }, [])

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!supabaseInitialized || !supabase) {
      setError("Клиент Supabase не инициализирован. Пожалуйста, обновите страницу.")
      return
    }

    setLoading(true)
    setError(null)

    try {
      console.log("Attempting to sign in with email:", email)

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        console.error("Login error:", error)
        throw error
      }

      console.log("Login successful:", data)
      toast({
        title: "Успешный вход",
        description: "Вы успешно вошли в систему",
      })

      // Перезагружаем страницу для обновления состояния аутентификации
      window.location.href = "/"
    } catch (err: any) {
      console.error("Login error details:", err)

      // Более понятные сообщения об ошибках для пользователя
      if (err.message?.includes("Invalid login")) {
        setError("Неверный email или пароль")
      } else if (err.message?.includes("Email not confirmed")) {
        setError("Email не подтвержден. Проверьте вашу почту.")
      } else {
        setError(err.message || "Ошибка входа. Пожалуйста, попробуйте позже.")
      }

      toast({
        title: "Ошибка входа",
        description: err.message || "Не удалось войти в систему",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!supabaseInitialized || !supabase) {
      setError("Клиент Supabase не инициализирован. Пожалуйста, обновите страницу.")
      return
    }

    setLoading(true)
    setError(null)

    try {
      console.log("Attempting to sign up with email:", email)

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) {
        console.error("Signup error:", error)
        throw error
      }

      console.log("Signup successful:", data)

      // Показываем сообщение об успехе
      setError("Проверьте вашу почту для подтверждения регистрации")
      toast({
        title: "Регистрация успешна",
        description: "Проверьте вашу почту для подтверждения регистрации",
      })
    } catch (err: any) {
      console.error("Signup error details:", err)

      // Более понятные сообщения об ошибках для пользователя
      if (err.message?.includes("already registered")) {
        setError("Этот email уже зарегистрирован")
      } else if (err.message?.includes("password")) {
        setError("Пароль должен содержать не менее 6 символов")
      } else {
        setError(err.message || "Ошибка регистрации. Пожалуйста, попробуйте позже.")
      }

      toast({
        title: "Ошибка регистрации",
        description: err.message || "Не удалось зарегистрироваться",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <div className="flex items-center gap-2 mb-2">
          <img src="/images/arina-logo.png" alt="Арина" className="h-10 w-10 rounded-full" />
          <CardTitle>Арина</CardTitle>
        </div>
        <CardDescription>Войдите или зарегистрируйтесь для доступа к вашим задачам</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="login">Вход</TabsTrigger>
            <TabsTrigger value="register">Регистрация</TabsTrigger>
          </TabsList>
          <TabsContent value="login">
            <form onSubmit={handleLogin} className="space-y-4 mt-4">
              <div className="space-y-2">
                <Input
                  id="email"
                  type="email"
                  placeholder="Email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Input
                  id="password"
                  type="password"
                  placeholder="Пароль"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
              {error && (
                <Alert variant={error.includes("Проверьте") ? "default" : "destructive"}>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              {!supabaseInitialized && (
                <Alert>
                  <AlertDescription>Инициализация подключения...</AlertDescription>
                </Alert>
              )}
              <Button type="submit" className="w-full" disabled={loading || !supabaseInitialized}>
                {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                Войти
              </Button>
            </form>
          </TabsContent>
          <TabsContent value="register">
            <form onSubmit={handleSignUp} className="space-y-4 mt-4">
              <div className="space-y-2">
                <Input
                  id="email"
                  type="email"
                  placeholder="Email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Input
                  id="password"
                  type="password"
                  placeholder="Пароль"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
              {error && (
                <Alert variant={error.includes("Проверьте") ? "default" : "destructive"}>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              {!supabaseInitialized && (
                <Alert>
                  <AlertDescription>Инициализация подключения...</AlertDescription>
                </Alert>
              )}
              <Button type="submit" className="w-full" disabled={loading || !supabaseInitialized}>
                {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                Зарегистрироваться
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
