"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { createClient } from "@/lib/supabase/client"
import { toast } from "@/hooks/use-toast"

// Define the form schema
const actionFormSchema = z.object({
  text: z.string().min(2, { message: "Текст должен содержать минимум 2 символа" }),
  group_id: z.string().optional(),
})

type ActionFormValues = z.infer<typeof actionFormSchema>

interface ActionFormProps {
  action?: any // The action data for editing
  groups?: any[] // List of task groups for selection
}

export function ActionForm({ action, groups = [] }: ActionFormProps) {
  const router = useRouter()
  const supabase = createClient()
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Initialize form with action data or defaults
  const form = useForm<ActionFormValues>({
    resolver: zodResolver(actionFormSchema),
    defaultValues: action
      ? {
          ...action,
        }
      : {
          text: "",
          group_id: "",
        },
  })

  async function onSubmit(data: ActionFormValues) {
    setIsSubmitting(true)

    try {
      if (action) {
        // Update existing action
        const { error } = await supabase.from("actions").update(data).eq("id", action.id)

        if (error) throw error

        toast({
          title: "Действие обновлено",
          description: "Данные действия успешно обновлены",
        })
      } else {
        // Create new action
        const { data: newAction, error } = await supabase.from("actions").insert(data).select()

        if (error) throw error

        toast({
          title: "Действие создано",
          description: "Новое действие успешно добавлено",
        })
      }

      router.refresh()
      router.push("/manager/actions")
    } catch (error: any) {
      toast({
        title: "Ошибка",
        description: error.message || "Произошла ошибка при сохранении данных",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardContent className="pt-6">
            <div className="grid gap-4">
              <FormField
                control={form.control}
                name="text"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Текст действия</FormLabel>
                    <FormControl>
                      <Input placeholder="Например: Умывание" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="group_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Группа</FormLabel>
                    <FormControl>
                      <select
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        {...field}
                      >
                        <option value="">Выберите группу</option>
                        {groups.map((group) => (
                          <option key={group.id} value={group.id}>
                            {group.name}
                          </option>
                        ))}
                      </select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()} disabled={isSubmitting}>
            Отмена
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Сохранение..." : action ? "Обновить действие" : "Создать действие"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
