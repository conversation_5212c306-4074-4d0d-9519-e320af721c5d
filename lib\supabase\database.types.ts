export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[]

export interface Database {
  public: {
    Tables: {
      tasks: {
        Row: {
          id: string
          user_id: string
          text: string
          completed: boolean
          status: string
          comment: string
          created_at: string
          updated_at: string
          execution_date: string | null
          execution_time: string | null
          interval_id: string | null
          group_id: string | null
          created_by: string | null
        }
        Insert: {
          id?: string
          user_id: string
          text: string
          completed?: boolean
          status?: string
          comment?: string
          created_at?: string
          updated_at?: string
          execution_date?: string | null
          execution_time?: string | null
          interval_id?: string | null
          group_id?: string | null
          created_by?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          text?: string
          completed?: boolean
          status?: string
          comment?: string
          created_at?: string
          updated_at?: string
          execution_date?: string | null
          execution_time?: string | null
          interval_id?: string | null
          group_id?: string | null
          created_by?: string | null
        }
      }
      task_intervals: {
        Row: {
          id: string
          name: string
          start_time: string
          end_time: string
        }
        Insert: {
          id?: string
          name: string
          start_time: string
          end_time: string
        }
        Update: {
          id?: string
          name?: string
          start_time?: string
          end_time?: string
        }
      }
      task_groups: {
        Row: {
          id: string
          name: string
          icon: string | null
        }
        Insert: {
          id?: string
          name: string
          icon?: string | null
        }
        Update: {
          id?: string
          name?: string
          icon?: string | null
        }
      }
      tags: {
        Row: {
          id: string
          user_id: string
          name: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          created_at?: string
        }
      }
      task_tags: {
        Row: {
          task_id: string
          tag_id: string
        }
        Insert: {
          task_id: string
          tag_id: string
        }
        Update: {
          task_id?: string
          tag_id?: string
        }
      }
      task_history: {
        Row: {
          id: string
          user_id: string
          task_id: string
          action: string
          details: Json
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          task_id: string
          action: string
          details?: Json
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          task_id?: string
          action?: string
          details?: Json
          created_at?: string
        }
      }
    }
  }
}
