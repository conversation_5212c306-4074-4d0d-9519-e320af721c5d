import { createServerClient } from "@/lib/supabase/server"
import { PlanForm } from "../../plan-form"
import { notFound } from "next/navigation"

export default async function EditPlanPage({ params }: { params: { id: string } }) {
  const supabase = await createServerClient()

  // Загружаем данные плана
  const { data: plan, error } = await supabase
    .from("plans")
    .select(`
      *,
      client:client_id(id, full_name)
    `)
    .eq("id", params.id)
    .single()

  if (error || !plan) {
    notFound()
  }

  // Загружаем список всех клиентов
  const { data: clients, error: clientsError } = await supabase
    .from("clients")
    .select("id, full_name")
    .order("full_name", { ascending: true })

  if (clientsError) {
    console.error("Error loading clients:", clientsError)
  }

  // Загружаем список действий
  const { data: actions, error: actionsError } = await supabase
    .from("actions")
    .select("*")
    .order("text", { ascending: true })

  if (actionsError) {
    console.error("Error loading actions:", actionsError)
  }

  // Загружаем список интервалов
  const { data: intervals, error: intervalsError } = await supabase
    .from("task_intervals")
    .select("*")
    .order("name", { ascending: true })

  if (intervalsError) {
    console.error("Error loading intervals:", intervalsError)
  }

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Редактировать план</h1>
      <PlanForm
        plan={plan}
        client={plan.client}
        clients={clients || []}
        actions={actions || []}
        intervals={intervals || []}
      />
    </div>
  )
}
