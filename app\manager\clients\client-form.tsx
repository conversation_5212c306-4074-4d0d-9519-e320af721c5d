"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { createClient } from "@/lib/supabase/client"
import { toast } from "@/hooks/use-toast"
import { format } from "date-fns"

// Define the form schema
const clientFormSchema = z.object({
  full_name: z.string().min(2, { message: "ФИО должно содержать минимум 2 символа" }),
  birth_date: z.string().optional(),

  // Документы
  has_oms: z.boolean().default(false),
  has_dms: z.boolean().default(false),
  has_snils: z.boolean().default(false),
  has_disability_certificate: z.boolean().default(false),
  medical_documents: z.string().optional(),

  // Врачи и медицинские учреждения
  doctor_name: z.string().optional(),
  doctor_phone: z.string().optional(),
  social_worker_name: z.string().optional(),
  social_worker_phone: z.string().optional(),
  state_clinic_address: z.string().optional(),
  state_clinic_phone: z.string().optional(),
  commercial_clinic_address: z.string().optional(),
  commercial_clinic_phone: z.string().optional(),
  home_doctor_phone: z.string().optional(),
  emergency_service_phone: z.string().optional(),

  // Бытовая информация
  management_company_phone: z.string().optional(),
  other_household_services: z.string().optional(),

  // Аллергии и непереносимости
  medication_allergies: z.string().optional(),
  food_allergies: z.string().optional(),
  other_allergies: z.string().optional(),

  // Закупки и предпочтения
  product_procurement: z.string().optional(),
  medication_procurement: z.string().optional(),
  habits: z.string().optional(),
  food_preferences: z.string().optional(),
  food_exclusions: z.string().optional(),
  doctor_recommended_diet: z.string().optional(),
  pets: z.string().optional(),

  // Связь с пользователем
  user_id: z.string().optional(),
})

type ClientFormValues = z.infer<typeof clientFormSchema>

interface ClientFormProps {
  client?: any // The client data for editing
  users?: any[] // List of users for selection
}

export function ClientForm({ client, users = [] }: ClientFormProps) {
  const router = useRouter()
  const supabase = createClient()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [relatives, setRelatives] = useState<any[]>(client?.relatives || [])

  // Initialize form with client data or defaults
  const form = useForm<ClientFormValues>({
    resolver: zodResolver(clientFormSchema),
    defaultValues: client
      ? {
          ...client,
          birth_date: client.birth_date ? format(new Date(client.birth_date), "yyyy-MM-dd") : "",
        }
      : {
          full_name: "",
          birth_date: "",
          has_oms: false,
          has_dms: false,
          has_snils: false,
          has_disability_certificate: false,
        },
  })

  async function onSubmit(data: ClientFormValues) {
    setIsSubmitting(true)

    try {
      if (client) {
        // Update existing client
        const { error } = await supabase.from("clients").update(data).eq("id", client.id)

        if (error) throw error

        toast({
          title: "Клиент обновлен",
          description: "Данные клиента успешно обновлены",
        })
      } else {
        // Create new client
        const { data: newClient, error } = await supabase.from("clients").insert(data).select()

        if (error) throw error

        toast({
          title: "Клиент создан",
          description: "Новый клиент успешно добавлен",
        })

        // Redirect to the new client page
        if (newClient && newClient.length > 0) {
          router.push(`/manager/clients/${newClient[0].id}`)
        } else {
          router.push("/manager/clients")
        }
      }

      router.refresh()
    } catch (error: any) {
      toast({
        title: "Ошибка",
        description: error.message || "Произошла ошибка при сохранении данных",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Tabs defaultValue="main" className="w-full">
          <TabsList className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 mb-4">
            <TabsTrigger value="main">Основное</TabsTrigger>
            <TabsTrigger value="documents">Документы</TabsTrigger>
            <TabsTrigger value="medical">Медицинская информация</TabsTrigger>
            <TabsTrigger value="household">Бытовая информация</TabsTrigger>
            <TabsTrigger value="allergies">Аллергии</TabsTrigger>
            <TabsTrigger value="preferences">Предпочтения</TabsTrigger>
          </TabsList>

          <TabsContent value="main" className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="full_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>ФИО клиента</FormLabel>
                        <FormControl>
                          <Input placeholder="Иванов Иван Иванович" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="birth_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Дата рождения</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {users.length > 0 && (
                    <FormField
                      control={form.control}
                      name="user_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Связанный пользователь</FormLabel>
                          <FormControl>
                            <select
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              {...field}
                            >
                              <option value="">Не выбран</option>
                              {users.map((user) => (
                                <option key={user.id} value={user.id}>
                                  {user.email}
                                </option>
                              ))}
                            </select>
                          </FormControl>
                          <FormDescription>
                            Пользователь, который будет выполнять задачи для этого клиента
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <h3 className="text-lg font-medium mb-4">Родственники/опекуны/персонал</h3>

                {/* We'll implement this part later */}
                <div className="text-sm text-gray-500">Функционал добавления родственников будет реализован позже</div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                <div className="grid gap-4">
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="has_oms"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Копия ОМС</FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="has_dms"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Копия ДМС</FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="has_snils"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Копия СНИЛС</FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="has_disability_certificate"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Копия справки об инвалидности</FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="medical_documents"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Копии медицинских документов</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Перечислите имеющиеся медицинские документы"
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="medical" className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="doctor_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Лечащий врач</FormLabel>
                        <FormControl>
                          <Input placeholder="ФИО врача" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="doctor_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Телефон врача</FormLabel>
                        <FormControl>
                          <Input placeholder="+7 (XXX) XXX-XX-XX" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="social_worker_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Соц. работник</FormLabel>
                        <FormControl>
                          <Input placeholder="ФИО соц. работника" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="social_worker_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Телефон соц. работника</FormLabel>
                        <FormControl>
                          <Input placeholder="+7 (XXX) XXX-XX-XX" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="state_clinic_address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Гос. поликлиника, адрес</FormLabel>
                        <FormControl>
                          <Input placeholder="Адрес поликлиники" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="state_clinic_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Телефон гос. поликлиники</FormLabel>
                        <FormControl>
                          <Input placeholder="+7 (XXX) XXX-XX-XX" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="commercial_clinic_address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Комм. клиника, адрес</FormLabel>
                        <FormControl>
                          <Input placeholder="Адрес клиники" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="commercial_clinic_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Телефон комм. клиники</FormLabel>
                        <FormControl>
                          <Input placeholder="+7 (XXX) XXX-XX-XX" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="home_doctor_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Врач на дом, телефон</FormLabel>
                        <FormControl>
                          <Input placeholder="+7 (XXX) XXX-XX-XX" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="emergency_service_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Вызов СМП, телефон</FormLabel>
                        <FormControl>
                          <Input placeholder="+7 (XXX) XXX-XX-XX" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="household" className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                <div className="grid gap-4">
                  <FormField
                    control={form.control}
                    name="management_company_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Управляющая компания (экстренный ремонт, сантехник, электрик), телефон</FormLabel>
                        <FormControl>
                          <Input placeholder="+7 (XXX) XXX-XX-XX" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="other_household_services"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Другие бытовые услуги, наименования, контакты</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Перечислите другие бытовые услуги"
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="product_procurement"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Закупка продуктов (способ, контакт, ответственный)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Опишите процесс закупки продуктов"
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="medication_procurement"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Закупка лекарств и гигиенических средств (способ, контакт, ответственный)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Опишите процесс закупки лекарств"
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="pets"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Домашние питомцы, вид/кличка</FormLabel>
                        <FormControl>
                          <Input placeholder="Например: кошка Мурка, собака Рекс" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="allergies" className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                <div className="grid gap-4">
                  <FormField
                    control={form.control}
                    name="medication_allergies"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>На лекарства</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Перечислите аллергии на лекарства"
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="food_allergies"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>На продукты питания</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Перечислите аллергии на продукты"
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="other_allergies"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Другие</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Перечислите другие аллергии" className="min-h-[100px]" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preferences" className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                <div className="grid gap-4">
                  <FormField
                    control={form.control}
                    name="habits"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Привычки</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Опишите привычки клиента" className="min-h-[100px]" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="food_preferences"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Пищевые предпочтения</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Опишите пищевые предпочтения" className="min-h-[100px]" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="food_exclusions"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Пищевые исключения</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Опишите пищевые исключения" className="min-h-[100px]" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="doctor_recommended_diet"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Диета, рекомендованная врачом</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Опишите рекомендованную диету" className="min-h-[100px]" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()} disabled={isSubmitting}>
            Отмена
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Сохранение..." : client ? "Обновить клиента" : "Создать клиента"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
