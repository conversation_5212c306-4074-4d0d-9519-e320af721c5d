import { createServerClient } from "@/lib/supabase/server"
import { DefaultPlanForm } from "../default-plan-form"

export default async function NewDefaultPlanPage() {
  const supabase = await createServerClient()

  // Fetch actions for selection
  const { data: actions } = await supabase.from("actions").select("*").order("text", { ascending: true })

  // Fetch intervals for selection
  const { data: intervals } = await supabase.from("task_intervals").select("*").order("name", { ascending: true })

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Создать дефолтный план</h1>
      <DefaultPlanForm actions={actions} intervals={intervals} />
    </div>
  )
}
