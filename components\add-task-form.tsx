"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { ru } from "date-fns/locale"
import { CalendarIcon, Clock } from "lucide-react"
import { createTask } from "@/lib/actions"
import { useToast } from "@/hooks/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"
import type { TaskInterval, TaskGroup } from "@/lib/actions"

interface AddTaskFormProps {
  intervals: TaskInterval[]
  groups: TaskGroup[]
}

export function AddTaskForm({ intervals, groups }: AddTaskFormProps) {
  const [text, setText] = useState("")
  const [comment, setComment] = useState("")
  const [date, setDate] = useState<Date>(new Date())
  const [time, setTime] = useState("")
  const [intervalId, setIntervalId] = useState("none")
  const [groupId, setGroupId] = useState("none")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isCalendarOpen, setIsCalendarOpen] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!text.trim()) {
      setError("Введите текст задачи")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const formattedDate = format(date, "yyyy-MM-dd")
      const finalIntervalId = intervalId === "none" ? null : intervalId
      const finalGroupId = groupId === "none" ? null : groupId

      const { task, error } = await createTask(text, formattedDate, time || null, finalIntervalId, finalGroupId)

      if (error) {
        throw new Error(error)
      }

      toast({
        title: "Задача создана",
        description: "Задача успешно добавлена в список",
      })

      // Перенаправляем на главную страницу
      router.push("/")
    } catch (err: any) {
      setError(err.message || "Не удалось создать задачу")
      toast({
        title: "Ошибка",
        description: err.message || "Не удалось создать задачу",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Генерируем список времени с шагом 30 минут
  const timeOptions = []
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const formattedHour = hour.toString().padStart(2, "0")
      const formattedMinute = minute.toString().padStart(2, "0")
      timeOptions.push(`${formattedHour}:${formattedMinute}`)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Новая задача</CardTitle>
        <CardDescription>Заполните форму для создания новой задачи</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <label htmlFor="text" className="text-sm font-medium">
              Текст задачи
            </label>
            <Input
              id="text"
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Введите текст задачи"
              required
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="date" className="text-sm font-medium">
              Дата выполнения
            </label>
            <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left font-normal" id="date">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "d MMMM yyyy", { locale: ru }) : "Выберите дату"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={(date) => {
                    if (date) {
                      setDate(date)
                      setIsCalendarOpen(false)
                    }
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <label htmlFor="time" className="text-sm font-medium">
              Время выполнения
            </label>
            <Select value={time} onValueChange={setTime}>
              <SelectTrigger id="time" className="w-full">
                <SelectValue placeholder="Выберите время">
                  {time ? (
                    <div className="flex items-center">
                      <Clock className="mr-2 h-4 w-4" />
                      {time}
                    </div>
                  ) : (
                    "Выберите время"
                  )}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="not_specified">Не указано</SelectItem>
                {timeOptions.map((time) => (
                  <SelectItem key={time} value={time}>
                    {time}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label htmlFor="interval" className="text-sm font-medium">
              Интервал
            </label>
            <Select value={intervalId} onValueChange={setIntervalId}>
              <SelectTrigger id="interval" className="w-full">
                <SelectValue placeholder="Выберите интервал" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Не указан</SelectItem>
                {intervals.map((interval) => (
                  <SelectItem key={interval.id} value={interval.id}>
                    {interval.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label htmlFor="group" className="text-sm font-medium">
              Группа
            </label>
            <Select value={groupId} onValueChange={setGroupId}>
              <SelectTrigger id="group" className="w-full">
                <SelectValue placeholder="Выберите группу" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Не указана</SelectItem>
                {groups.map((group) => (
                  <SelectItem key={group.id} value={group.id}>
                    {group.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label htmlFor="comment" className="text-sm font-medium">
              Комментарий
            </label>
            <Textarea
              id="comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Добавьте комментарий к задаче (необязательно)"
              rows={3}
            />
          </div>

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Создание..." : "Создать задачу"}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
