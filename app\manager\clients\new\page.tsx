import { createServerClient } from "@/lib/supabase/server"
import { ClientForm } from "../client-form"

export default async function NewClientPage() {
  const supabase = await createServerClient()

  // Fetch users for selection
  const { data: users } = await supabase.auth.admin.listUsers()

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Добавить клиента</h1>
      <ClientForm users={users?.users} />
    </div>
  )
}
