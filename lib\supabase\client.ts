"use client"

import type { Database } from "./database.types"
import { createBrowserClient } from "@supabase/ssr"
import { SupabaseClient } from "@supabase/supabase-js"

// Создаем синглтон для клиента Supabase
let supabaseClient: SupabaseClient<Database> | null = null

export const createClient = () => {
  if (!supabaseClient) {
    try {
      // Проверяем наличие переменных окружения
      if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
        console.error("Missing Supabase environment variables")
        throw new Error("Missing Supabase environment variables")
      }

      console.log("Initializing Supabase client with URL:", process.env.NEXT_PUBLIC_SUPABASE_URL)

      supabaseClient = createBrowserClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );

      console.log("Supabase client initialized successfully")
    } catch (error) {
      console.error("Error creating Supabase client:", error)
      throw new Error("Failed to initialize Supabase client")
    }
  }
  return supabaseClient
}

// Функция для сброса клиента (используется при ошибках)
export const resetClient = () => {
  console.log("Resetting Supabase client")
  supabaseClient = null
}
